import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import {
  Component,
  EventEmitter,
  Inject,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  QueryList,
  SimpleChanges,
  TemplateRef,
  ViewChildren,
} from '@angular/core';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { ParticipantService, SubjectParticipant } from '@galaxy/conversation/core';
import { WhitelabelTranslationService } from '@galaxy/snapshot';
import { TranslateService } from '@ngx-translate/core';
import { LifecycleStage } from '@vendasta/account-group';
import { ConversationChannel, GlobalParticipantType, Participant } from '@vendasta/conversation';
import { AttendeeInfo, InstantMeetingConfig, RedirectToSettingsConfig } from '@vendasta/integrations';
import { CurrentInterface } from '@vendasta/snapshot';
import { BehaviorSubject, Observable, ReplaySubject, Subscription, combineLatest, firstValueFrom } from 'rxjs';
import { map, shareReplay, startWith, take } from 'rxjs/operators';
import { <PERSON>Checker, SSCAccessService } from '../../access';
import { ContactShadow, ContactsV2Service } from '../../common/contacts';
import { AllContactFields } from '../../common/contacts/contact-field.enum';
import { SALESPERSON_ID_TOKEN } from '../../common/providers';
import { ADD_TO_CAMPAIGNS_TOKEN } from '../../common/providers/tokens';
import { DynamicOpenCloseTemplateRefService } from '../../common/side-drawer/dynamic-open-close-template-ref.service';
import { SnapshotReportSource } from '../../data-providers/snapshot-report';
import {
  SNAPSHOT_LIMIT_SERVICE_TOKEN,
  SnapshotCountSDKWrapperService,
} from '../../easy-account-create/business/providers/snapshot-limit.service';
import { ACCESS_SNAPSHOT_LISTING_SCAN_PCC_SCC_TOKEN, Features } from '../../features';
import { Configuration, PARTNER_CONFIG_TOKEN } from '../../partner';
import { ScreenShareBusinessInfo } from '../../screenshare/screenshare.component';
import { INTEGRATIONS_PAGE } from '../../urls';
import { UiBusiness } from '../manage-accounts-ui.service';

function contactHasEmail(c: ContactShadow): boolean {
  return !!c?.baseContact?.contactEmail;
}

@Component({
  selector: 'app-account-card',
  templateUrl: './account-card.component.html',
  styleUrls: ['./account-card.component.scss'],
  standalone: false,
})
export class AccountCardComponent implements OnInit, OnChanges, OnDestroy {
  contacts$: Observable<ContactShadow[]>;
  private readonly redirectConfig = <RedirectToSettingsConfig>{ authPageUrl: INTEGRATIONS_PAGE };
  private readonly viewBusiness$$ = new ReplaySubject<UiBusiness>(1);
  readonly shareScreenInfo$ = this.viewBusiness$$.pipe(
    map((business) => {
      return <ScreenShareBusinessInfo>{
        businessId: business.accountGroupId,
        partnerId: business.partnerId,
        countryCode: business.country,
      };
    }),
  );

  meetingConfig$: Observable<InstantMeetingConfig>;
  subscriptions: Subscription[] = [];
  private readonly lifecycleStage$$ = new ReplaySubject<string>(1);
  readonly lifecycleStage$ = this.lifecycleStage$$.asObservable();
  readonly mobile$: Observable<boolean> = this.breakpointObserver.observe(Breakpoints.Handset).pipe(
    map((results) => results.matches),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  @Input() business: UiBusiness;
  @Input() zoomEnabled: boolean;
  @Input() meetEnabled: boolean;
  @Output() snapshotRefreshClickedEvent: EventEmitter<UiBusiness> = new EventEmitter<UiBusiness>();
  @Output() snapshotCreateClickedEvent: EventEmitter<UiBusiness> = new EventEmitter<UiBusiness>();
  @Output() openComposerClickedEvent: EventEmitter<UiBusiness> = new EventEmitter<UiBusiness>();
  @Output() campaignClick: EventEmitter<UiBusiness> = new EventEmitter<UiBusiness>();
  @ViewChildren('openScorecard') openScorecardTemplate: QueryList<TemplateRef<any>>;
  readonly showProducts$: Observable<boolean> = this.configuration$.pipe(map((config) => !config.hideProducts));
  readonly snapshotName$: Observable<string> = this.configuration$.pipe(
    map((config) => config.snapshotName || this.whitelabelTranslate.instant('SNAPSHOT_REPORT.SNAPSHOT_REPORT')),
  );
  exceedLimit$: Observable<boolean>;
  snapshotExpired$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  snapshotExpired$: Observable<boolean> = this.snapshotExpired$$.asObservable();
  triggerUpdate$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  triggerUpdate$: Observable<boolean> = this.triggerUpdate$$.asObservable();
  showInbox$: Observable<boolean> = this.access.hasAccessToInbox(this.inboxFeatureFlag$);
  private readonly currentSnapshot$$ = new ReplaySubject<CurrentInterface>(1);
  public readonly currentSnapshot$ = this.currentSnapshot$$.asObservable();
  subjectParticipants: SubjectParticipant[];
  currentIAMParticipant$ = this.participantService.buildIAMUserParticipant() as Observable<Participant>;
  readonly conversationChannel = ConversationChannel.CONVERSATION_CHANNEL_INTERNAL;

  constructor(
    private readonly breakpointObserver: BreakpointObserver,
    @Inject(PARTNER_CONFIG_TOKEN) private readonly configuration$: Observable<Configuration>,
    @Inject(SNAPSHOT_LIMIT_SERVICE_TOKEN) readonly snapshotLimitService: SnapshotCountSDKWrapperService,
    @Inject(SALESPERSON_ID_TOKEN) readonly salespersonId$: Observable<string>,
    private readonly contactsService: ContactsV2Service,
    private readonly translate: TranslateService,
    private readonly whitelabelTranslate: WhitelabelTranslationService,
    private readonly dynamicOpenCloseService: DynamicOpenCloseTemplateRefService,
    private readonly snapshotProvider: SnapshotReportSource,
    private readonly participantService: ParticipantService,
    @Inject(Features.Inbox) public readonly inboxFeatureFlag$: Observable<boolean>,
    @Inject(SSCAccessService) private readonly access: AccessChecker,
    @Inject(ACCESS_SNAPSHOT_LISTING_SCAN_PCC_SCC_TOKEN) readonly displaySnapshotListingScan$: Observable<string>,
    @Inject(ADD_TO_CAMPAIGNS_TOKEN) public readonly canSendCampaigns$: Observable<boolean>,
  ) {}

  ngOnInit(): void {
    this.setCurrentSnapshot(null);
    this.fetchSnapshot();
    this.exceedLimit$ = this.snapshotLimitService.hasExceededSnapshotLimit$;
    this.contacts$ = this.contactsService.list$(this.business.accountGroupId, ...AllContactFields);

    const attendeeSelections$ = this.contacts$.pipe(
      map((contacts: ContactShadow[]) =>
        (contacts || <ContactShadow[]>[]).filter(contactHasEmail).map(
          (contact: ContactShadow) =>
            <AttendeeInfo>{
              name: contact.fullName,
              uniqueIdentifier: contact.contactEmail,
            },
        ),
      ),
    );

    this.meetingConfig$ = combineLatest([
      this.viewBusiness$$.asObservable(),
      this.salespersonId$,
      attendeeSelections$,
    ]).pipe(
      map(
        ([biz, spid, attendeeSelections]) =>
          <InstantMeetingConfig>{
            showAttendeeSelector: true,
            attendeeSelections: attendeeSelections,
            redirectToSettingsConfig: this.redirectConfig,
            trackingFields: {
              accountGroupId: biz.accountGroupId,
              salesPersonId: spid,
            },
          },
      ),
    );

    this.setLifecycleStageString(this.business?.marketingInfo?.lifecycleStage);

    this.subjectParticipants = [
      new SubjectParticipant({
        internalParticipantId: this.business.partnerId,
        participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_PARTNER,
      }),
      new SubjectParticipant({
        internalParticipantId: this.business.accountGroupId,
        participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_ACCOUNT_GROUP,
      }),
    ];
  }

  ngOnChanges(changes: SimpleChanges): void {
    const businessChanged = Boolean(changes.business);
    if (businessChanged) {
      const business = changes.business.currentValue as UiBusiness;
      this.viewBusiness$$.next(business);
    }
  }

  setLifecycleStageString(lifecycle: LifecycleStage): void {
    switch (lifecycle) {
      case LifecycleStage.LIFECYCLE_STAGE_LEAD:
        this.lifecycleStage$$.next('COMMON.LIFECYCLE_STAGES.LEAD');
        break;
      case LifecycleStage.LIFECYCLE_STAGE_PROSPECT:
        this.lifecycleStage$$.next('COMMON.LIFECYCLE_STAGES.PROSPECT');
        break;
      case LifecycleStage.LIFECYCLE_STAGE_CUSTOMER:
        this.lifecycleStage$$.next('COMMON.LIFECYCLE_STAGES.CUSTOMER');
        break;
      default:
        break;
    }
  }

  showSnapshotLite = (business: UiBusiness): boolean => !!business && business.country === 'US';

  snapshotListingUrl = (business: UiBusiness): string => (business ? `/snapshot/lite/${business.accountGroupId}` : '');

  mailToContactEmailLink(email: string): string {
    return `mailto:${email}`;
  }

  snapshotRefreshClickEvent(): void {
    this.snapshotRefreshClickedEvent.emit(this.business);
  }

  openComposerClickEvent(): void {
    this.openComposerClickedEvent.emit(this.business);
  }

  snapshotCreateClickEvent(): void {
    this.snapshotCreateClickedEvent.emit(this.business);
  }

  campaignSendMail(): void {
    this.campaignClick.emit(this.business);
  }

  get showSalespersonAction(): boolean {
    return this.business.salesPersonAction !== 'account-created';
  }

  openViewScorecard(): void {
    this.triggerUpdate$.pipe(take(1)).subscribe((trigger) => this.triggerUpdate$$.next(!trigger));
    this.subscriptions.push(
      this.openScorecardTemplate.changes.pipe(startWith(this.openScorecardTemplate), take(1)).subscribe((ref) => {
        this.dynamicOpenCloseService.registerTemplate('openScorecard', ref.first);
        this.dynamicOpenCloseService.open('openScorecard');
      }),
    );
  }

  closeDrawer(): void {
    this.dynamicOpenCloseService.close();
  }

  onSnapshotRenewPressed(): void {
    this.dynamicOpenCloseService.close();
    this.snapshotRefreshClickEvent();
  }

  private async fetchSnapshot(): Promise<void> {
    const snapshot = await firstValueFrom(this.snapshotProvider.get$(this.business.accountGroupId));

    if (snapshot) {
      this.setCurrentSnapshot(snapshot);
      this.setSnapshotExpired(snapshot?.expired);
    } else {
      this.business.latestSnapshotExpiry = null;
    }
  }

  private setCurrentSnapshot(snapshot: CurrentInterface): void {
    this.currentSnapshot$$.next(snapshot);
  }

  private setSnapshotExpired(expiredDate: Date): void {
    this.snapshotExpired$$.next(expiredDate < new Date());
  }

  ngOnDestroy(): void {
    this.dynamicOpenCloseService.close();
    this.subscriptions.forEach((s) => s.unsubscribe());
  }
}
