<div class="content">
  <ng-container>
    <form [formGroup]="form">
      <automata-automation-replacement-chips
        [label]="'AUTOMATIONS.EDITOR.TASKS.TRIGGER_WEBHOOK.PANEL.METHOD' | translate"
        [dataService]="httpMethodService"
        [formControl]="form.controls.method"
        [isRequired]="true"
        [multiple]="false"
        [supportDataPassing]="false"
      ></automata-automation-replacement-chips>
      <glxy-form-field required="true" prefixText="https://">
        <glxy-label>
          {{ 'AUTOMATIONS.EDITOR.TASKS.TRIGGER_WEBHOOK.PANEL.WEBHOOK_URL' | translate }}
        </glxy-label>
        <input matInput type="text" formControlName="endpoint_url" [required]="true" />
        <glxy-error *ngIf="form.controls.endpoint_url?.hasError('required') && form.controls.endpoint_url.dirty">
          {{ 'AUTOMATIONS.EDITOR.TASKS.SEND_EMAIL_NOTIFICATION.REQUIRED_FIELD' | translate }}
        </glxy-error>
        <glxy-error *ngIf="form.controls.endpoint_url?.hasError('url') && form.controls.endpoint_url.dirty">
          {{ 'AUTOMATIONS.VALIDATORS.INVALID_URL' | translate }}
        </glxy-error>
        <glxy-error *ngIf="form.controls.endpoint_url?.hasError('secure') && form.controls.endpoint_url.dirty">
          {{ 'AUTOMATIONS.EDITOR.TASKS.TRIGGER_WEBHOOK.PANEL.WEBHOOK_URL_SECURE' | translate }}
        </glxy-error>
        <glxy-hint *ngIf="!contextConfig.whitelabel">
          <div [innerHTML]="'AUTOMATIONS.EDITOR.TASKS.TRIGGER_WEBHOOK.PANEL.HELP_ARTICLE_LINK' | translate"></div>
        </glxy-hint>
      </glxy-form-field>
      <mat-accordion [multi]="true" displayMode="flat">
        <mat-expansion-panel>
          <mat-expansion-panel-header>
            <mat-panel-title>
              {{ 'AUTOMATIONS.EDITOR.TASKS.OUTGOING_WEBHOOK.PANEL.QUERY_PARAMS' | translate }}
            </mat-panel-title>
          </mat-expansion-panel-header>
          <automata-key-value-input [formArray]="form.controls.query_params"></automata-key-value-input>
        </mat-expansion-panel>
        <mat-expansion-panel>
          <mat-expansion-panel-header>
            <mat-panel-title>
              {{ 'AUTOMATIONS.EDITOR.TASKS.OUTGOING_WEBHOOK.PANEL.HEADERS' | translate }}
            </mat-panel-title>
          </mat-expansion-panel-header>
          <automata-key-value-input [formArray]="form.controls.custom_headers"></automata-key-value-input>
        </mat-expansion-panel>
        <mat-expansion-panel>
          <mat-expansion-panel-header>
            <mat-panel-title>
              {{ 'AUTOMATIONS.EDITOR.TASKS.OUTGOING_WEBHOOK.PANEL.COOKIES' | translate }}
            </mat-panel-title>
          </mat-expansion-panel-header>
          <automata-key-value-input [formArray]="form.controls.cookies"></automata-key-value-input>
        </mat-expansion-panel>
        <mat-expansion-panel>
          <mat-expansion-panel-header>
            <mat-panel-title>
              {{ 'AUTOMATIONS.EDITOR.TASKS.OUTGOING_WEBHOOK.PANEL.JSON_BODY' | translate }}
            </mat-panel-title>
          </mat-expansion-panel-header>
          <automata-key-value-input
            [formArray]="form.controls.json_body"
            [useTextArea]="true"
          ></automata-key-value-input>
          <div class="payload-header">
            {{ 'AUTOMATIONS.EDITOR.RETURN_VALUES.EXPECTED_PAYLOAD' | translate }}
          </div>
          <div class="payload">
            &#123;<br />
            <ng-container *ngFor="let c of $any(form.controls.json_body).controls; let last = last">
              <ng-container *ngIf="c.get('key').value; else noValues">
                &nbsp;&nbsp;&nbsp;&nbsp;<span class="payload-key">"{{ c.get('key').value }}":</span>
                <span class="payload-value">"{{ c.get('value').value }}"</span>
                <ng-container *ngIf="!last">,</ng-container>
                <br />
              </ng-container>
              <ng-template #noValues></ng-template>
            </ng-container>
            &#125;<br />
          </div>
        </mat-expansion-panel>
      </mat-accordion>
    </form>
    <button
      id="copy-button"
      mat-button
      color="primary"
      [cdkCopyToClipboard]="generateCurlCommand()"
      (click)="reassureTheUserThatTheButtonDidSomething()"
    >
      <mat-icon class="material-icons-outlined">file_copy</mat-icon>
      <span>{{ 'AUTOMATIONS.EDITOR.TASKS.OUTGOING_WEBHOOK.PANEL.COPY_AS_CURL' | translate }}</span>
    </button>
    <span class="to-test">{{ 'AUTOMATIONS.EDITOR.TASKS.OUTGOING_WEBHOOK.PANEL.TO_TEST' | translate }}</span>
  </ng-container>
</div>
