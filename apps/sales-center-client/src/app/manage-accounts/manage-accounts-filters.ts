import {
  Check<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Date<PERSON>icker<PERSON>ilter<PERSON>ield,
  FilterSection,
  MultiSelectFilterField,
  RadioFilterField,
  SearchSelectFilterField,
} from '@vendasta/uikit';
import { SalespersonFilter } from '../salespeople/filter/salesperson.filter';
import { SubscriptionList } from '@vendasta/rx-utils';
import { ChangeDetectorRef } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Archived, Campaign, LifecycleStage, Snapshot } from '@vendasta/sales';

export const ManageAccountFilterId = {
  SALESPERSON: 'salesperson',
  SALESTEAM: 'salesTeam',
  SALES_STATUS: 'sales_status',
  ACTION: 'action',
  CREATED_START_DATE: 'createdAfterDateISO',
  CREATED_END_DATE: 'createdBeforeDateISO',
  ARCHIVE_STATUS: 'archiveStatus',
  BUSINESS_CATEGORY: 'taxIdFilter',
  ON_CAMPAIGN: 'campaignFilterFlag',
  SNAPSHOT_SENT: 'snapshotSentFilterFlag',
  ACCOUNT_TAGS: 'accountTags',
  GOALS: 'goals',
  TRAINING_PRIORITIES: 'trainingPriorities',
  LIFECYCLE_STAGE: 'lifecycleStage',
};

export enum SalesStatusFilterId {
  UNREAD_ACTIVITY = 'unread',
  ACCOUNT_CREATED = 'account-created',
  READY_TO_SELL = 'ready-to-sell',
  IN_PROGRESS = 'in-progress',
  FOLLOW_UP_NEEDED = 'follow-up-needed',
  CLOSED_WON = 'closed-won',
  CLOSED_LOST = 'closed-lost',
}

export enum SalespersonActionId {
  ANY = '',
  EMAIL_SENT = 'email-sent',
  EMAIL_RECEIVED = 'email-received',
  INBOUND_CALL = 'inbound-call',
  OUTBOUND_CALL = 'outbound-call',
  MEETING = 'meeting',
  OPPORTUNITY_CREATED = 'opportunity-created',
  OPPORTUNITY_CLOSED_WON = 'opportunity-closed-won',
  OPPORTUNITY_CLOSED_LOST = 'opportunity-closed-lost',
  OPPORTUNITY_UPDATED = 'opportunity-updated',
}

export interface SimpleManageAccountsFilter {
  name: string;
  value: string;
}

export class ManageAccountsFilters {
  private readonly subscriptions = SubscriptionList.new();

  salesPersonFilterSection: SalespersonFilter;
  salesTeamFilterSection: FilterSection;
  salesStatusFilterSection: FilterSection;
  salesPersonActionFilterSection: FilterSection;
  createdDateFilterSection: FilterSection;
  archivedFilterSection: FilterSection;
  snapshotSentFilterSection: FilterSection;
  onCampaignFilterSection: FilterSection;
  moreOptionsFilterSection: FilterSection;
  businessPrioritiesSection: FilterSection;
  noOpportunityActionFilterSection: FilterSection;
  noOpportunityStatusFilterSection: FilterSection;
  lifecycleFilterSection: FilterSection;

  readonly lifecycleFilter = new RadioFilterField({
    name: this.translate('COMMON.LIFECYCLE_STAGE'),
    id: ManageAccountFilterId.LIFECYCLE_STAGE,
    options: [],
    value: LifecycleStage.LIFECYCLE_STAGE_UNSET,
  });

  readonly salesTeamFilter: SearchSelectFilterField = new SearchSelectFilterField({
    name: 'COMMON.SALES_TEAM_LABEL',
    id: ManageAccountFilterId.SALESTEAM,
    optionDisplayProperty: 'teamName',
    value: null,
    placeholder: this.translate('MANAGE_ACCOUNTS.SALES_TEAM_FILTER_PLACEHOLDER'),
    showSearch: true,
  });
  readonly unreadActivityFilter: CheckboxFilterField = new CheckboxFilterField({
    name: 'COMMON.SALES_STATUS.UNREAD_ACTIVITY',
    id: SalesStatusFilterId.UNREAD_ACTIVITY,
    appliedTextPrefix: this.translate('COMMON.SALES_STATUS_LABEL'),
    value: false,
  });
  readonly accountCreatedFilter: CheckboxFilterField = new CheckboxFilterField({
    name: 'COMMON.SALES_STATUS.ACCOUNT_CREATED',
    id: SalesStatusFilterId.ACCOUNT_CREATED,
    appliedTextPrefix: this.translate('COMMON.SALES_STATUS_LABEL'),
    value: false,
  });
  readonly readyToSellFilter: CheckboxFilterField = new CheckboxFilterField({
    name: 'COMMON.SALES_STATUS.READY_TO_SELL',
    id: SalesStatusFilterId.READY_TO_SELL,
    appliedTextPrefix: this.translate('COMMON.SALES_STATUS_LABEL'),
    value: false,
  });
  readonly inProgressFilter: CheckboxFilterField = new CheckboxFilterField({
    name: 'COMMON.SALES_STATUS.IN_PROGRESS',
    id: SalesStatusFilterId.IN_PROGRESS,
    appliedTextPrefix: this.translate('COMMON.SALES_STATUS_LABEL'),
    value: false,
  });
  readonly followUpNeededFilter: CheckboxFilterField = new CheckboxFilterField({
    name: 'COMMON.SALES_STATUS.FOLLOW_UP_NEEDED',
    id: SalesStatusFilterId.FOLLOW_UP_NEEDED,
    appliedTextPrefix: this.translate('COMMON.SALES_STATUS_LABEL'),
    value: false,
  });
  readonly closedWonFilter: CheckboxFilterField = new CheckboxFilterField({
    name: 'COMMON.SALES_STATUS.CLOSED_WON',
    id: SalesStatusFilterId.CLOSED_WON,
    appliedTextPrefix: this.translate('COMMON.SALES_STATUS_LABEL'),
    value: false,
  });

  readonly closedLostFilter: CheckboxFilterField = new CheckboxFilterField({
    name: 'COMMON.SALES_STATUS.CLOSED_LOST',
    id: SalesStatusFilterId.CLOSED_LOST,
    appliedTextPrefix: this.translate('COMMON.SALES_STATUS_LABEL'),
    value: false,
  });

  readonly salesPersonActionFilter: RadioFilterField = new RadioFilterField({
    name: 'COMMON.SALES_TERMS.ACTION',
    id: ManageAccountFilterId.ACTION,
    options: [
      { name: 'COMMON.ANY', id: SalespersonActionId.ANY },
      { name: 'COMMON.SALES_ACTIONS.EMAIL_SENT', id: SalespersonActionId.EMAIL_SENT },
      { name: 'COMMON.SALES_ACTIONS.EMAIL_RECEIVED', id: SalespersonActionId.EMAIL_RECEIVED },
      { name: 'COMMON.SALES_ACTIONS.INBOUND_CALL', id: SalespersonActionId.INBOUND_CALL },
      { name: 'COMMON.SALES_ACTIONS.OUTBOUND_CALL', id: SalespersonActionId.OUTBOUND_CALL },
      { name: 'COMMON.SALES_ACTIONS.MEETING', id: SalespersonActionId.MEETING },
      { name: 'COMMON.SALES_ACTIONS.OPPORTUNITY_CREATED', id: SalespersonActionId.OPPORTUNITY_CREATED },
      { name: 'COMMON.SALES_ACTIONS.OPPORTUNITY_CLOSED_WON', id: SalespersonActionId.OPPORTUNITY_CLOSED_WON },
      { name: 'COMMON.SALES_ACTIONS.OPPORTUNITY_CLOSED_LOST', id: SalespersonActionId.OPPORTUNITY_CLOSED_LOST },
      { name: 'COMMON.SALES_ACTIONS.OPPORTUNITY_UPDATED', id: SalespersonActionId.OPPORTUNITY_UPDATED },
    ],
    value: { name: 'COMMON.ANY', id: SalespersonActionId.ANY },
    appliedTextPrefix: this.translate('COMMON.SALES_TERMS.ACTION'),
  });

  readonly noOpportunityActionFilter: RadioFilterField = new RadioFilterField({
    name: 'COMMON.SALES_TERMS.ACTION',
    id: ManageAccountFilterId.ACTION,
    options: [
      { name: 'COMMON.ANY', id: SalespersonActionId.ANY },
      { name: 'COMMON.SALES_ACTIONS.EMAIL_SENT', id: SalespersonActionId.EMAIL_SENT },
      { name: 'COMMON.SALES_ACTIONS.EMAIL_RECEIVED', id: SalespersonActionId.EMAIL_RECEIVED },
      { name: 'COMMON.SALES_ACTIONS.INBOUND_CALL', id: SalespersonActionId.INBOUND_CALL },
      { name: 'COMMON.SALES_ACTIONS.OUTBOUND_CALL', id: SalespersonActionId.OUTBOUND_CALL },
      { name: 'COMMON.SALES_ACTIONS.MEETING', id: SalespersonActionId.MEETING },
    ],
    value: { name: 'COMMON.ANY', id: SalespersonActionId.ANY },
    appliedTextPrefix: this.translate('COMMON.SALES_TERMS.ACTION'),
  });

  readonly createdDateFromFilter = new DatePickerFilterField({
    name: 'COMMON.DATES.FROM_LABEL',
    id: ManageAccountFilterId.CREATED_START_DATE,
    appliedTextPrefix: this.translate('COMMON.ATTRIBUTES.CREATED_DATE'),
    value: null,
  });

  readonly creatededDateToFilter = new DatePickerFilterField({
    name: 'COMMON.DATES.TO_LABEL',
    id: ManageAccountFilterId.CREATED_END_DATE,
    appliedTextPrefix: this.translate('COMMON.ATTRIBUTES.CREATED_DATE'),
    value: null,
  });

  readonly archivedFilter = new RadioFilterField({
    name: 'COMMON.ATTRIBUTES.ARCHIVED',
    id: ManageAccountFilterId.ARCHIVE_STATUS,
    options: [
      {
        name: 'COMMON.ATTRIBUTES.NOT_ARCHIVED',
        id: Archived.ARCHIVED_NO,
      },
      {
        name: 'COMMON.ATTRIBUTES.ARCHIVED',
        id: Archived.ARCHIVED_YES,
      },
      {
        name: 'COMMON.ALL',
        id: Archived.ARCHIVED_ANY,
      },
    ],
    value: {
      name: 'COMMON.ATTRIBUTES.NOT_ARCHIVED',
      id: Archived.ARCHIVED_NO,
    },
    appliedTextPrefix: this.translate('COMMON.ATTRIBUTES.ARCHIVED'),
  });

  readonly businessCategoryFilter = new MultiSelectFilterField({
    name: 'COMMON.ACCOUNT_ATTRIBUTES.BUSINESS_CATEGORY',
    id: ManageAccountFilterId.BUSINESS_CATEGORY,
    options: [],
    optionDisplayProperty: 'name',
    value: [],
    placeholderText: this.translate('COMMON.ACCOUNT_ATTRIBUTES.BUSINESS_CATEGORY'),
  });

  readonly onCampaignFilter = new RadioFilterField({
    name: 'MANAGE_ACCOUNTS.ON_CAMPAIGN_TITLE',
    id: ManageAccountFilterId.ON_CAMPAIGN,
    options: [
      {
        name: 'COMMON.ALL',
        id: Campaign.CAMPAIGN_ANY,
      },
      {
        name: 'MANAGE_ACCOUNTS.ON_CAMPAIGN_DECLARATION',
        id: Campaign.CAMPAIGN_ON,
      },
      {
        name: 'MANAGE_ACCOUNTS.NOT_ON_CAMPAIGN_DECLARATION',
        id: Campaign.CAMPAIGN_OFF,
      },
    ],
    value: {
      name: 'COMMON.ALL',
      id: Campaign.CAMPAIGN_ANY,
    },
  });

  readonly snapshotSentFilter = new RadioFilterField({
    name: 'COMMON.ACCOUNT_ATTRIBUTES.SNAPSHOT_SENT',
    id: ManageAccountFilterId.SNAPSHOT_SENT,
    options: [
      {
        name: this.translate('COMMON.ALL'),
        id: Snapshot.SNAPSHOT_ANY,
      },
      {
        name: this.translate('COMMON.ACCOUNT_ATTRIBUTES.SNAPSHOT_SENT'),
        id: Snapshot.SNAPSHOT_SENT,
      },
      {
        name: this.translate('MANAGE_ACCOUNTS.SNAPSHOT_NOT_SENT'),
        id: Snapshot.SNAPSHOT_NOT_SENT,
      },
    ],
    value: {
      name: this.translate('COMMON.ALL'),
      id: Snapshot.SNAPSHOT_ANY,
    },
  });

  readonly accountTagsFilter = new MultiSelectFilterField({
    name: 'COMMON.ACCOUNT_ATTRIBUTES.ACCOUNT_TAGS',
    id: ManageAccountFilterId.ACCOUNT_TAGS,
    options: [],
    optionDisplayProperty: 'name',
    placeholderText: this.translate('COMMON.ACCOUNT_ATTRIBUTES.ACCOUNT_TAGS'),
    limitResults: true,
    value: [],
  });

  readonly goalFilter = new SearchSelectFilterField({
    name: 'Goal',
    id: ManageAccountFilterId.GOALS,
    options: [],
    optionDisplayProperty: 'name',
    placeholder: 'Select Goal',
    value: null,
    showSearch: true,
    dropdownWidth: '200px',
  });

  constructor(private readonly translate: (key: string | Array<string>) => string | any) {
    this.lifecycleFilterSection = new FilterSection({
      title: 'COMMON.LIFECYCLE_STAGE',
      type: 'and',
      fields: [this.lifecycleFilter],
      collapsed: true,
    });
    this.lifecycleFilterSection.hideSection = true;
    this.salesPersonFilterSection = new SalespersonFilter(
      'COMMON.SALESPERSON_LABEL',
      this.translate('MANAGE_ACCOUNTS.SELECT_SALESPERSON'),
    );
    this.salesTeamFilterSection = new FilterSection({
      title: 'COMMON.SALES_TEAM_LABEL',
      type: 'or',
      fields: [this.salesTeamFilter],
    });
    this.salesStatusFilterSection = new FilterSection({
      title: 'COMMON.SALES_TERMS.STATUS',
      type: 'or',
      fields: [
        this.unreadActivityFilter,
        this.accountCreatedFilter,
        this.readyToSellFilter,
        this.inProgressFilter,
        this.followUpNeededFilter,
        this.closedWonFilter,
        this.closedLostFilter,
      ],
    });
    this.noOpportunityStatusFilterSection = new FilterSection({
      title: 'COMMON.SALES_TERMS.STATUS',
      type: 'or',
      fields: [
        this.unreadActivityFilter,
        this.accountCreatedFilter,
        this.readyToSellFilter,
        this.inProgressFilter,
        this.followUpNeededFilter,
      ],
    });
    this.salesPersonActionFilterSection = new FilterSection({
      title: 'COMMON.SALES_TERMS.ACTION',
      type: 'or',
      collapsed: true,
      fields: [this.salesPersonActionFilter],
    });
    this.noOpportunityActionFilterSection = new FilterSection({
      title: 'COMMON.SALES_TERMS.ACTION',
      type: 'or',
      collapsed: true,
      fields: [this.noOpportunityActionFilter],
    });
    this.createdDateFilterSection = new FilterSection({
      title: 'COMMON.ATTRIBUTES.CREATED_DATE',
      type: 'or',
      collapsed: true,
      fields: [this.createdDateFromFilter, this.creatededDateToFilter],
    });
    this.archivedFilterSection = new FilterSection({
      title: 'COMMON.ATTRIBUTES.ARCHIVED',
      type: 'or',
      collapsed: true,
      fields: [this.archivedFilter],
    });
    this.onCampaignFilterSection = new FilterSection({
      title: 'MANAGE_ACCOUNTS.ON_CAMPAIGN_INQUIRY',
      type: 'or',
      collapsed: true,
      fields: [this.onCampaignFilter],
    });
    this.snapshotSentFilterSection = new FilterSection({
      title: 'MANAGE_ACCOUNTS.SNAPSHOT_SENT_INQUIRY',
      type: 'or',
      collapsed: true,
      fields: [this.snapshotSentFilter],
    });
    this.moreOptionsFilterSection = new FilterSection({
      title: 'COMMON.MORE_OPTIONS_LABEL',
      type: 'or',
      collapsed: true,
      fields: [this.accountTagsFilter, this.businessCategoryFilter],
    });
    this.businessPrioritiesSection = new FilterSection({
      title: 'Strategy',
      type: 'or',
      collapsed: false,
      fields: [this.goalFilter],
    });
  }

  public subscribeToTranslator(translator: TranslateService, changeDetector: ChangeDetectorRef): void {
    const filters: FilterSection[] = [
      this.salesPersonFilterSection,
      this.salesTeamFilterSection,
      this.salesStatusFilterSection,
      this.noOpportunityStatusFilterSection,
      this.salesPersonActionFilterSection,
      this.noOpportunityActionFilterSection,
      this.createdDateFilterSection,
      this.archivedFilterSection,
      this.onCampaignFilterSection,
      this.snapshotSentFilterSection,
      this.moreOptionsFilterSection,
      this.businessPrioritiesSection,
    ];
    filters.forEach((filter) => {
      this.subscriptions.addAll(...filter.subscribeToTranslator(translator, changeDetector, true));
    });
  }

  destroy(): void {
    this.subscriptions.destroy();
  }
}
