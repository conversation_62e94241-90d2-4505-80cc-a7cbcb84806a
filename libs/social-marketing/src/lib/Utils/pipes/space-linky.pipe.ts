import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'spaceLinky',
  standalone: false,
})
export class SpaceLinkyPipe implements PipeTransform {
  transform(text: string): string {
    if (!text) return text;

    const urlPattern = /(\b(https?:\/\/|www\.)[^\s]+|(?<!@)\b[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\/?[^\s]*)/g;

    text = text.replace(urlPattern, function (url) {
      return ' ' + url;
    });

    return text;
  }
}
