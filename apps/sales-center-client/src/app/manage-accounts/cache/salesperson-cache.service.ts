import { Injectable } from '@angular/core';
import { Salesperson } from '@galaxy/types';
import { AppCacheService, CacheEntry, ClientOrigin } from '@vendasta/sales-ui';

export interface SalespersonCacheEntry {
  salespeople: Salesperson[];
  ownerId: string;
}

@Injectable()
export class SalespersonCacheService {
  private readonly origin = ClientOrigin.MANAGE_ACCOUNTS_SALESPEOPLE;
  constructor(private readonly appCache: AppCacheService) {}
  public getCachedSalespeople(id: string): SalespersonCacheEntry | null {
    const cacheResponse = this.appCache.get(this.origin);
    const cacheValue = cacheResponse.value as SalespersonCacheEntry;
    return cacheResponse.isCacheHit && cacheValue.ownerId === id
      ? <SalespersonCacheEntry>{
          salespeople: cacheResponse.value.salespeople,
          ownerId: cacheResponse.value.ownerId,
        }
      : null;
  }
  public add(salespersonCacheEntry: SalespersonCacheEntry): void {
    const entry = <CacheEntry>{
      origin: this.origin,
      value: JSON.stringify(salespersonCacheEntry),
    };
    this.appCache.add(entry);
  }
}
