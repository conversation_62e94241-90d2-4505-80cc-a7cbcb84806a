import { Inject, Injectable, Optional } from '@angular/core';
import { AtlasLanguageService } from '@galaxy/atlas/core';
import { Salesperson as SSCSalesperson } from '@galaxy/types';
import { TranslateService } from '@ngx-translate/core';
import { MarketingInfo } from '@vendasta/account-group';
import { SubscriptionList } from '@vendasta/rx-utils';
import { OpportunityCount, SalesOpportunitiesSdk, SalesOpportunitiesService } from '@vendasta/sales-opportunities';
import { Salesperson } from '@vendasta/salesperson';
import moment from 'moment';
import { Observable, SchedulerLike, combineLatest } from 'rxjs';
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  map,
  scan,
  shareReplay,
  skip,
  startWith,
  switchMap,
  take,
  withLatestFrom,
} from 'rxjs/operators';
import { ActivityTypes } from '../account-details/recent-activity/enums';
import { USER_INFO_TOKEN } from '../core/feature-flag.service';
import { ALL_SALESPEOPLE_STARTING_WITH_CURRENT_TOKEN } from '../data-providers/salespeople';
import { LoggedInUserInfo } from '../logged-in-user-info';
import { ACCOUNT_INFO_ROOT } from '../urls';
import { parseEvents } from './manage-accounts-filters/manage-accounts-filters.component';
import { ManageAccountsFiltersService } from './manage-accounts-filters/manage-accounts-filters.service';
import {
  Business,
  BusinessLoader,
  LoadMoreBusinessEvent,
  LoadMoreValues,
  ManageAccountsService,
  SortKeys,
} from './manage-accounts.service';

// formatter for address on manage accounts contact card
// see test by example how this could be used for more generic purpose
export function formatBusinessAddress(address: string, city: string, state: string, zip: string): string {
  let formattedAddress = '';

  if (address) {
    formattedAddress += `${address}<br>`;
  }

  const secondLine = [city, state, zip].filter((v) => v).join(', ');
  formattedAddress += secondLine;
  return formattedAddress;
}

export interface UiBusiness extends Business {
  daysSinceLastCustomerActivity$: Observable<string>;
  daysSinceLastSalesActivity$: Observable<string>;
  daysSinceLastConnected$: Observable<string>;
  salesStatusColor: string;
  flames: Array<any>;
  manageAccountUrl: string;
  salesPersonName$: Observable<string>;
  salesPersonExists: boolean;
  formattedAddress: string;
  opportunitiesTagFormatted$?: Observable<string>;
  snapshotExpired: boolean;
  timeUntilSnapshotExpires$: Observable<string>;
  marketingInfo: MarketingInfo;
}

const SalesStatusColors = {
  'ready-to-sell': 'blue',
  'in-progress': 'purple',
  'follow-up-needed': 'yellow',
  'closed-won': 'positive',
  'closed-lost': 'warn',
};

const initiallySortAscending = false;

@Injectable()
export class ManageAccountsUiService {
  readonly businesses$: Observable<UiBusiness[]>;
  readonly totalBusinesses$: Observable<number>;
  readonly salesPerson$: Observable<Salesperson>;
  readonly salesPeople$: Observable<Salesperson[]>;
  readonly loadMoreValues$: Observable<LoadMoreValues>;
  readonly searchingWithEmptyTeam$: Observable<boolean>;
  readonly businessesLoading$: Observable<boolean>;
  readonly businessesLoadingMore$: Observable<boolean>;
  readonly hasBusinessResults$: Observable<boolean>;
  readonly isBusinessLoadSuccessful$: Observable<boolean>;
  readonly isBusinessLoadMoreSuccessful$: Observable<boolean>;
  readonly isSortAscending$: Observable<boolean>;
  private readonly subscriptions = SubscriptionList.new();

  hasPageData$ = this.filterService.hasPageData$;

  constructor(
    @Inject(ManageAccountsService) private readonly manageAccountsService: BusinessLoader,
    @Inject(ALL_SALESPEOPLE_STARTING_WITH_CURRENT_TOKEN) private readonly salespeople$: Observable<SSCSalesperson[]>,
    @Inject(SalesOpportunitiesService) private readonly opportunitiesSdk: SalesOpportunitiesSdk,
    @Inject(USER_INFO_TOKEN) private readonly userInfo$: Observable<LoggedInUserInfo>,
    private readonly i18n: TranslateService,
    private readonly locale: AtlasLanguageService,
    readonly filterService: ManageAccountsFiltersService,
    @Optional() @Inject('Scheduler') private readonly scheduler?: SchedulerLike, // For testability
    @Optional() @Inject('DEBOUNCE_MILLIS') DEBOUNCE_MILLIS?: number, // Injectable For testability
  ) {
    DEBOUNCE_MILLIS = DEBOUNCE_MILLIS || 500;

    this.salesPeople$ = salespeople$.pipe(
      map((sscsp) =>
        sscsp.map(
          (sp) =>
            <Salesperson>{
              partnerId: sp.partnerId,
              marketId: sp.marketId,
              salespersonId: sp.id,
              firstName: sp.fullName.split(' ')[0],
              lastName: sp.fullName.split(' ').reverse()[0],
              email: sp.email,
              fullName: sp.fullName,
            },
        ),
      ),
      shareReplay(1),
    );
    this.salesPerson$ = combineLatest([this.salesPeople$, this.manageAccountsService.salesPerson$]).pipe(
      map(([salesPeople, loggedInUser]: [Salesperson[], LoggedInUserInfo]) => {
        return salesPeople.find((sp) => sp.salespersonId === loggedInUser.salespersonId);
      }),
      distinctUntilChanged((sp1, sp2) => sp1.fullName === sp2.fullName && sp1.salespersonId === sp2.salespersonId),
      shareReplay(1),
    );

    this.businesses$ = combineLatest([this.manageAccountsService.businessResult$, this.userInfo$]).pipe(
      map(([businesses, userInfo]) =>
        this.addViewFieldsToBiz(businesses, this.salesPeople$, userInfo.partnerId, userInfo.marketId),
      ),
      shareReplay(1),
    );

    this.totalBusinesses$ = this.manageAccountsService.totalResults$;

    this.searchingWithEmptyTeam$ = this.manageAccountsService.searchingWithEmptyTeam$.pipe(distinctUntilChanged());

    this.loadMoreValues$ = this.manageAccountsService.loadMoreValues$;
    this.hasBusinessResults$ = this.businesses$.pipe(map((businesses) => businesses.length > 0));
    this.businessesLoading$ = this.manageAccountsService.businessState$.isLoading$;
    this.businessesLoadingMore$ = this.manageAccountsService.moreBusinessState$.isLoading$;
    this.isBusinessLoadSuccessful$ = this.manageAccountsService.businessState$.isSuccess$;

    this.isBusinessLoadMoreSuccessful$ = this.loadMoreValues$.pipe(
      switchMap((loadMoreValue) => {
        if (loadMoreValue.loadMore) {
          return this.manageAccountsService.moreBusinessState$.isSuccess$;
        }
        return this.manageAccountsService.businessState$.isSuccess$;
      }),
    );

    this.isSortAscending$ = this.filterService.sortToggles$.pipe(
      scan((prev) => !prev, initiallySortAscending),
      startWith(initiallySortAscending),
      shareReplay(1),
    );
    const sched = this.scheduler || undefined;
    // TODO: Move to filters service
    this.isSortAscending$
      .pipe(
        skip(1), // Don't trigger a reload for the initial sort
        debounceTime(DEBOUNCE_MILLIS, sched),
      )
      .subscribe((v) => this.manageAccountsService.initLoadBusinesses({ sortAscending: v }));

    this.filterService.searchTerm$.pipe(debounceTime(DEBOUNCE_MILLIS, sched)).subscribe((v) => {
      this.manageAccountsService.initLoadBusinesses({ searchTerm: v });
    });

    this.filterService.filters$.pipe(map((obj) => parseEvents(obj))).subscribe((val) => {
      this.manageAccountsService.initLoadBusinesses(val);
    });
    this.subscriptions.add(this.filterService.sortField$, (sortField) => this.changeSortField(sortField));
  }

  changeSortField(sortField: SortKeys): void {
    this.manageAccountsService.initLoadBusinesses({ sortValue: sortField });
  }

  private addViewFieldsToBiz(
    businesses: Business[],
    salesPeople$: Observable<Salesperson[]>,
    partnerId: string,
    marketId: string,
  ): UiBusiness[] {
    const businessIds = businesses.map((biz) => biz.accountGroupId);
    const opportunityCounts$ = this.getOpportunityCounts(businessIds, partnerId, marketId);
    const neverWord$ = this.i18n.stream('COMMON.DATES.NEVER');
    return businesses.map((biz) => {
      const daysLastCustomerActivity$ =
        biz.activityType !== ActivityTypes.ACCOUNT_CREATE &&
        !!biz.lastCustomerActivityDate &&
        biz.lastCustomerActivityDate.getTime() !== 0
          ? this.locale.language$.pipe(map(() => moment(biz.lastCustomerActivityDate).fromNow()))
          : neverWord$;
      const daysLastSalesActivity$ =
        !!biz.lastSalesActivityDate && biz.lastSalesActivityDate.getTime() !== 0
          ? this.locale.language$.pipe(map(() => moment(biz.lastSalesActivityDate).fromNow()))
          : neverWord$;
      const lastConnectedDate$ =
        !!biz.lastConnectedDate && biz.lastConnectedDate.getTime() !== 0
          ? this.locale.language$.pipe(map(() => moment(biz.lastConnectedDate).fromNow()))
          : neverWord$;
      const newFields = {
        snapshotExpired: biz.latestSnapshotExpiry < moment().toDate(),
        daysSinceLastConnected$: lastConnectedDate$,
        timeUntilSnapshotExpires$: this.locale.language$.pipe(map(() => moment(biz.latestSnapshotExpiry).fromNow())),
        daysSinceLastCustomerActivity$: daysLastCustomerActivity$,
        daysSinceLastSalesActivity$: daysLastSalesActivity$,
        salesStatusColor: SalesStatusColors[biz.salesStatusTag],
        flames: Array(biz.hotness).fill('🔥'),
        manageAccountUrl: `/${ACCOUNT_INFO_ROOT}/${biz.accountGroupId}`,
        salesPersonName$: salesPeople$.pipe(
          map((salesPeople) => salesPeople.find((person) => person.salespersonId === biz.salesPersonId)),
          map((salesPerson) => (salesPerson ? salesPerson.fullName : '')),
        ),
        salesPersonExists: Boolean(biz.salesPersonId),
        opportunitiesTagFormatted$: opportunityCounts$.pipe(
          map(
            (counts: OpportunityCount[]): OpportunityCount =>
              counts.find((c) => c.accountGroupId === biz.accountGroupId),
          ),
          filter((count) => count && count.count !== undefined),
          switchMap((count) =>
            count.count === 1
              ? this.i18n.stream('MANAGE_ACCOUNTS.OPEN_OPPORTUNITIES_SINGULAR')
              : this.i18n.stream('MANAGE_ACCOUNTS.OPEN_OPPORTUNITIES_MULTIPLE', { num: count.count }),
          ),
        ),
        formattedAddress: formatBusinessAddress(biz.address, biz.city, biz.state, biz.zip),
        marketingInfo: biz.marketingInfo ?? new MarketingInfo(),
      };
      return Object.assign(biz, newFields);
    });
  }

  private getOpportunityCounts(
    businessIds: string[],
    partnerId: string,
    marketId: string,
  ): Observable<OpportunityCount[]> {
    return this.opportunitiesSdk.getOpportunityCountByAccountGroups(businessIds, true, partnerId, marketId).pipe(
      filter((counts) => counts && counts.length > 0),
      shareReplay(1),
    );
  }

  LoadMoreBusinesses(): void {
    this.businesses$.pipe(withLatestFrom(this.loadMoreValues$), take(1)).subscribe(([businesses, loadMoreValues]) => {
      const e = <LoadMoreBusinessEvent>{
        loadMoreValues: loadMoreValues,
        currentBusiness: businesses,
      };
      this.manageAccountsService.loadMoreBusinesses(e);
    });
  }

  updateSortValue(value: string): void {
    this.filterService.changeSortField(value);
  }

  toggleSortDirection(): void {
    this.filterService.toggleSort();
  }
}
