<app-page [pageTitle]="'AIEO.RESULTS.PAGE_TITLE' | translate">
  <div class="results-container">
    <div class="results-header">
      <h1 class="results-title">{{ 'AIEO.RESULTS.TITLE' | translate }}</h1>
      <p class="results-subtitle">{{ 'AIEO.RESULTS.SUBTITLE' | translate }}</p>
    </div>

    <app-audit-results
      *ngIf="auditData"
      [auditData]="auditData"
      (runAnotherAudit)="onRunAnotherAudit()"
    ></app-audit-results>

    <div *ngIf="!auditData" class="no-data-container">
      <mat-card class="no-data-card">
        <mat-card-content>
          <p>{{ 'AIEO.RESULTS.NO_DATA.MESSAGE' | translate }}</p>
          <button mat-raised-button color="primary" (click)="onRunAnotherAudit()">
            {{ 'AIEO.RESULTS.NO_DATA.ACTION' | translate }}
          </button>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</app-page>
