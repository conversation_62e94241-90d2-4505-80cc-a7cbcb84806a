import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { DOCUMENT } from '@angular/common';
import { AfterViewChecked, ChangeDetectorRef, Component, Inject, OnDestroy, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatSelectChange } from '@angular/material/select';
import { MatTabChangeEvent } from '@angular/material/tabs';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { CreateRefreshDialogData, SnapshotAction, SnapshotCheckoutComponent } from '@galaxy/snapshot';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { SubscriptionList } from '@vendasta/rx-utils';
import { LifecycleStage } from '@vendasta/sales';
import { FilterService } from '@vendasta/uikit';

import {
  CampaignSidePanelDataInterface,
  SidePanelState,
  SidePanelStateData,
  SidePanelStateService,
  SlideOutPanelService,
} from '@vendasta/sales-ui';
import moment from 'moment';
import { BehaviorSubject, Observable, combineLatest, of } from 'rxjs';
import { map, shareReplay, startWith, switchMap, take } from 'rxjs/operators';
import { AccessChecker, SSCAccessService } from '../access';
import { AppPage } from '../access/page-access/app-page.enum';
import { BUSINESS_SEARCH_URL } from '../accounts/accounts.routing';
import { AjaxBusinessApiService, BusinessApiService } from '../business';
import { ACCESS_ALL_MARKET_TOKEN } from '../common/providers';
import {
  AddSalesActivityDialogComponent,
  AddSalesActivityDialogData,
} from '../common/sales-activity/add-sales-activity-dialog/add-sales-activity-dialog.component';
import { FeatureFlagService } from '../core';
import { Features, SHOW_CUSTOM_FIELDS_FILTER_LINK } from '../features';
import { INSTANT_MEETING_ADD_CONTACT_HANDLER } from '../meetings/factories/add-contact-handler/add-contact-handler';
import { LOG_JOIN_MEETING_SALES_ACTIVITY_TOKEN } from '../meetings/factories/join-meeting-sales-activity/join-meeting-sales-activity';
import { ACCOUNTS_CUSTOM_FIELDS, ACCOUNT_ANALYTICS, PARTNER_PROSPECT } from '../urls';
import { ManageAccountsFiltersComponent } from './manage-accounts-filters/manage-accounts-filters.component';
import { ManageAccountsUiService, UiBusiness } from './manage-accounts-ui.service';
import { LoadMoreValues } from './manage-accounts.service';

declare let window: any;
const SNAPSHOT_SOURCE = 'manage-accounts';

export enum FILTER_FIELDS {
  'SEARCH' = 'search',
  'FILTERS' = 'filters',
  'SALESPERSON' = 'salesperson',
}

export enum FILTER_FIELD_NAMES {
  SALESPERSON = 'Salesperson',
}

@Component({
  selector: 'app-manage-accounts',
  templateUrl: './manage-accounts.component.html',
  styleUrls: ['./manage-accounts.component.scss'],
  providers: [FilterService],
  standalone: false,
})
export class ManageAccountsComponent implements OnDestroy, AfterViewChecked {
  hasPageData$: Observable<boolean>;
  businesses$: Observable<UiBusiness[]>;
  businessesLoading$: Observable<boolean>;
  businessesLoadingMore$: Observable<boolean>;

  @ViewChild('accountFilters') accountFilters: ManageAccountsFiltersComponent;
  readonly FILTER_FIELDS = FILTER_FIELDS;

  loadMoreValues$: Observable<LoadMoreValues>;
  private readonly lastChangedTerm$$ = new BehaviorSubject<FILTER_FIELDS>(FILTER_FIELDS.FILTERS);
  readonly lastChangedTerm$ = this.lastChangedTerm$$.asObservable();
  subscriptions = SubscriptionList.new();
  private filtersLoaded = false;

  readonly hasBusinessResults$: Observable<boolean>;
  readonly totalBusinesses$: Observable<number>;
  readonly showTeamFilterWarning$: Observable<boolean>;
  readonly showSuccess$: Observable<boolean>;
  readonly showError$: Observable<boolean>;

  readonly arrowIcon$: Observable<string>;
  readonly breadcrumbs$: Observable<any>;

  public selectedSortOption = 'hotness';
  agidQueryParam$: Observable<string>;
  readonly toolbarText$: Observable<string>;

  zoomFeatureEnabled$: Observable<boolean>;
  meetFeatureEnabled$: Observable<boolean>;

  isSmallScreen$: Observable<boolean>;
  readonly sidePanelState$: Observable<SidePanelStateData<any>>;
  readonly SidePanelState = SidePanelState;

  readonly businessSearchUrl = `${BUSINESS_SEARCH_URL}`;

  private readonly accountAnalyticsUrl = `/${ACCOUNT_ANALYTICS}`;
  private readonly accountProspectsUrl = `/${PARTNER_PROSPECT}`;
  readonly accessPartnerProspects$: Observable<boolean>;

  constructor(
    private readonly uiService: ManageAccountsUiService,
    private readonly dialog: MatDialog,
    @Inject(SSCAccessService) readonly features: AccessChecker,
    @Inject(DOCUMENT) private readonly document: Document,
    private readonly i18n: TranslateService,
    private readonly changeDetector: ChangeDetectorRef,
    private readonly featureFlags: FeatureFlagService,
    private readonly routes: ActivatedRoute,
    private readonly router: Router,
    private readonly alertService: SnackbarService,
    private readonly sidePanelStateService: SidePanelStateService,
    private readonly slideOutPanelService: SlideOutPanelService,
    @Inject(AjaxBusinessApiService) private readonly businessApi: BusinessApiService,
    @Inject(LOG_JOIN_MEETING_SALES_ACTIVITY_TOKEN) private readonly salesActivityLogger: void,
    @Inject(INSTANT_MEETING_ADD_CONTACT_HANDLER) private readonly addContactHandler: void,
    @Inject(ACCESS_ALL_MARKET_TOKEN) readonly hasAccessToAllAccountsInMarket$: Observable<string>,
    @Inject(SHOW_CUSTOM_FIELDS_FILTER_LINK) readonly showCustomFieldsFilterLink$: Observable<string>,
    public breakpointObserver: BreakpointObserver,
  ) {
    this.sidePanelState$ = this.sidePanelStateService.sidePanelState$;
    this.refreshAppAfterLocalLogin();
    this.setFeatures();
    this.agidQueryParam$ = this.routes.queryParams.pipe(map((params) => params?.agid));
    this.breadcrumbs$ = i18n.stream('MANAGE_ACCOUNTS.TITLE').pipe(map((translation) => [{ label: translation }]));
    this.loadMoreValues$ = this.uiService.loadMoreValues$;
    this.businesses$ = this.uiService.businesses$;
    this.totalBusinesses$ = this.uiService.totalBusinesses$;
    this.toolbarText$ = combineLatest([this.businesses$, this.totalBusinesses$]).pipe(
      switchMap(([businesses, total]) => {
        return this.i18n.stream('COMMON.SHOWING_N_OF_TOTAL_RESULTS', {
          length: businesses.length,
          total: total,
        });
      }),
    );
    this.isSmallScreen$ = this.breakpointObserver.observe([Breakpoints.XSmall]).pipe(map((result) => result.matches));
    this.businessesLoading$ = this.uiService.businessesLoading$;
    this.businessesLoadingMore$ = this.uiService.businessesLoadingMore$;
    this.showSuccess$ = this.uiService.isBusinessLoadSuccessful$;
    this.showError$ = combineLatest([
      this.uiService.isBusinessLoadSuccessful$,
      this.uiService.isBusinessLoadMoreSuccessful$.pipe(startWith(true)),
    ]).pipe(
      map(([load, loadMore]) => !(load && loadMore)),
      shareReplay(1),
    );

    this.hasBusinessResults$ = this.uiService.hasBusinessResults$;
    this.arrowIcon$ = this.uiService.isSortAscending$.pipe(map((isAsc) => (isAsc ? 'arrow_upward' : 'arrow_downward')));

    this.hasPageData$ = this.uiService.hasPageData$.pipe(
      take(1),
      map((inputs) => inputs.every((v) => v === true)),
      startWith(false),
      shareReplay(1),
    );

    this.showTeamFilterWarning$ = this.uiService.searchingWithEmptyTeam$;
    this.accessPartnerProspects$ = this.features.hasAccessToPage(AppPage.PartnerProspectTablePage);
  }

  loadMore(): void {
    this.uiService.LoadMoreBusinesses();
  }

  ngAfterViewChecked(): void {
    if (!!this.accountFilters && !this.filtersLoaded) {
      this.filtersLoaded = true;

      this.subscriptions.add(this.accountFilters.manageAccountsFiltersService.searchTerm$, () =>
        this.lastChangedTerm$$.next(FILTER_FIELDS.SEARCH),
      );

      this.subscriptions.add(this.accountFilters.manageAccountsFiltersService.filters$, (filters) => {
        if (filters.length === 1 && filters[0].filterFieldName === FILTER_FIELD_NAMES.SALESPERSON) {
          this.lastChangedTerm$$.next(FILTER_FIELDS.SALESPERSON);
        } else {
          this.lastChangedTerm$$.next(FILTER_FIELDS.FILTERS);
        }
      });
    }
  }

  private setFeatures(): void {
    this.zoomFeatureEnabled$ = this.featureFlags
      .featureFlagEnabled$(Features.ZoomFeatures)
      .pipe(take(1), shareReplay(1));
    this.meetFeatureEnabled$ = this.featureFlags
      .featureFlagEnabled$(Features.MeetFeature)
      .pipe(take(1), shareReplay(1));
  }

  openSnapshotRefreshModal(business: UiBusiness): void {
    const snapshotModalDialogRef = this.dialog.open(SnapshotCheckoutComponent, {
      width: '480px',
      data: {
        accountGroupId: business.accountGroupId,
        accountGroupName: business.name,
        formattedExpiry: business.latestSnapshotExpiry ? moment(business.latestSnapshotExpiry).fromNow() : null,
        action: SnapshotAction.Refresh,
        snapshotOriginDetails: SNAPSHOT_SOURCE,
      } as CreateRefreshDialogData,
    });
    snapshotModalDialogRef.afterClosed().subscribe((snapshotRefreshed: boolean) => {
      if (snapshotRefreshed) {
        business.snapshotExpired = false;
        business.timeUntilSnapshotExpires$ = of('');
      }
    });
  }

  snapshotCreateClickEvent(business: UiBusiness): void {
    const snapshotModalDialogRef = this.dialog.open(SnapshotCheckoutComponent, {
      width: '480px',
      data: {
        accountGroupId: business.accountGroupId,
        accountGroupName: business.name,
        formattedExpiry: business.latestSnapshotExpiry ? moment(business.latestSnapshotExpiry).fromNow() : null,
        action: SnapshotAction.Create,
        snapshotOriginDetails: SNAPSHOT_SOURCE,
      } as CreateRefreshDialogData,
    });
    snapshotModalDialogRef.afterClosed().subscribe((snapshotCreated: boolean) => {
      if (snapshotCreated) {
        business.latestSnapshotExpiry = moment().add(7, 'day').toDate();
      }
    });
  }

  openComposerModal(business: UiBusiness): void {
    const accountGroupId = business.accountGroupId;
    const accountGroupName = business.name;
    this.dialog.open(AddSalesActivityDialogComponent, {
      data: <AddSalesActivityDialogData>{
        accountGroupId: accountGroupId,
        accountGroupName: accountGroupName,
        marketId: business.marketId,
        partnerId: business.partnerId,
      },
    });
  }

  campaignSendMail(business: UiBusiness): void {
    this.sidePanelStateService.switchState(SidePanelState.campaignCreate, <CampaignSidePanelDataInterface>{
      accountGroupId: business.accountGroupId,
    });
    this.slideOutPanelService.openSlideOut();
  }

  ngOnDestroy(): void {
    this.subscriptions.destroy();
  }

  newSort($event: MatSelectChange): void {
    this.uiService.updateSortValue($event.value);
  }

  toggleSort(): void {
    this.uiService.toggleSortDirection();
  }

  private refreshAppAfterLocalLogin(): void {
    const subscription = this.router.events.subscribe((evt) => {
      if (!(evt instanceof NavigationEnd)) {
        return;
      }
      if (document.location.href.indexOf('fromLocalLogin') >= 0) {
        this.alertService.openSuccessSnack('Refreshing page to engage user session');
        window.location = window.location.pathname; // Removes query params
        return;
      } else {
        subscription.unsubscribe();
      }
    });
  }

  businessSearchClick(): void {
    this.router.navigateByUrl(this.businessSearchUrl);
  }

  openEmailSearchPage(): void {
    this.router.navigateByUrl('/accounts/search').catch(() => {
      this.alertService.openErrorSnack('ERRORS.UNABLE_TO_OPEN_PAGE');
    });
  }

  handleFilterCustomFieldsClick(): void {
    this.router.navigateByUrl(ACCOUNTS_CUSTOM_FIELDS).catch(() => {
      this.alertService.openErrorSnack('ERRORS.UNABLE_TO_OPEN_PAGE');
    });
  }

  lifecycleTabChanged(tab: MatTabChangeEvent): void {
    const tabIndex: LifecycleStage = tab?.index || LifecycleStage.LIFECYCLE_STAGE_UNSET;
    this.accountFilters.setLifecycleStage(tabIndex);
  }

  goToAnalytics(): void {
    this.router.navigateByUrl(this.accountAnalyticsUrl);
  }

  goToProspects(): void {
    this.router.navigateByUrl(this.accountProspectsUrl);
  }
}
