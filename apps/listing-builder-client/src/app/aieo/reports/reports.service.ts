import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { AuditReport } from './reports.interface';
import { AuditData } from '../audit-results/audit.interface';

@Injectable({
  providedIn: 'root',
})
export class ReportsService {
  private reportsSubject = new BehaviorSubject<AuditReport[]>([]);
  private currentReportSubject = new BehaviorSubject<AuditReport | null>(null);

  public reports$ = this.reportsSubject.asObservable();
  public currentReport$ = this.currentReportSubject.asObservable();

  constructor() {
    this.loadReportsFromStorage();
  }

  getReports(): Observable<AuditReport[]> {
    return this.reports$;
  }

  addReport(brandName: string, websiteUrl: string): string {
    const newReport: AuditReport = {
      id: this.generateId(),
      brandName,
      websiteUrl,
      status: 'pending',
      createdAt: new Date(),
    };

    const currentReports = this.reportsSubject.value;
    const updatedReports = [newReport, ...currentReports];
    this.reportsSubject.next(updatedReports);
    this.saveReportsToStorage(updatedReports);

    return newReport.id;
  }

  updateReportStatus(reportId: string, status: AuditReport['status'], auditData?: AuditData): void {
    const currentReports = this.reportsSubject.value;
    const reportIndex = currentReports.findIndex((report) => report.id === reportId);

    if (reportIndex !== -1) {
      const updatedReport = {
        ...currentReports[reportIndex],
        status,
        ...(status === 'completed' && { completedAt: new Date() }),
        ...(auditData && { auditData }),
      };

      const updatedReports = [...currentReports];
      updatedReports[reportIndex] = updatedReport;

      this.reportsSubject.next(updatedReports);
      this.saveReportsToStorage(updatedReports);
    }
  }

  updateReportProgress(reportId: string, progress: number): void {
    const currentReports = this.reportsSubject.value;
    const reportIndex = currentReports.findIndex((report) => report.id === reportId);

    if (reportIndex !== -1) {
      const updatedReport = {
        ...currentReports[reportIndex],
        progress,
      };

      const updatedReports = [...currentReports];
      updatedReports[reportIndex] = updatedReport;

      this.reportsSubject.next(updatedReports);
      this.saveReportsToStorage(updatedReports);
    }
  }

  setCurrentReport(report: AuditReport): void {
    this.currentReportSubject.next(report);
  }

  getCurrentReport(): AuditReport | null {
    return this.currentReportSubject.value;
  }

  clearCurrentReport(): void {
    this.currentReportSubject.next(null);
  }

  deleteReport(reportId: string): void {
    const currentReports = this.reportsSubject.value;
    const updatedReports = currentReports.filter((report) => report.id !== reportId);
    this.reportsSubject.next(updatedReports);
    this.saveReportsToStorage(updatedReports);
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private saveReportsToStorage(reports: AuditReport[]): void {
    try {
      localStorage.setItem('aieo_reports', JSON.stringify(reports));
    } catch (error) {
      console.error('Error saving reports to localStorage:', error);
    }
  }

  private loadReportsFromStorage(): void {
    try {
      const storedReports = localStorage.getItem('aieo_reports');
      if (storedReports) {
        const reports = JSON.parse(storedReports).map((report: any) => ({
          ...report,
          createdAt: new Date(report.createdAt),
          completedAt: report.completedAt ? new Date(report.completedAt) : undefined,
        }));
        this.reportsSubject.next(reports);
      }
    } catch (error) {
      console.error('Error loading reports from localStorage:', error);
    }
  }
}
