<app-page [pageTitle]="'AIEO.INPUT.PAGE_TITLE' | translate">
  <div class="aieo-nav-tabs">
    <button mat-button class="nav-tab" [class.active]="true" routerLink="./" routerLinkActive="active">
      <mat-icon>input</mat-icon>
      {{ 'AIEO.NAV.INPUT' | translate }}
    </button>
    <button mat-button class="nav-tab" routerLink="./reports" routerLinkActive="active">
      <mat-icon>assessment</mat-icon>
      {{ 'AIEO.NAV.REPORTS' | translate }}
    </button>
  </div>

  <div class="input-container">
    <div class="input-header" [class.hidden]="showSuccessMessage">
      <h1 class="input-title">{{ 'AIEO.INPUT.TITLE' | translate }}</h1>
      <p class="input-subtitle">{{ 'AIEO.INPUT.SUBTITLE' | translate }}</p>
    </div>

    <div *ngIf="showSuccessMessage" class="success-message-container" [@fadeInOut]>
      <div class="success-message-card">
        <div class="success-icon">
          <mat-icon>check_circle</mat-icon>
        </div>
        <div class="success-content">
          <h2 class="success-title">{{ 'AIEO.INPUT.SUCCESS.TITLE' | translate }}</h2>
          <p class="success-text">{{ 'AIEO.INPUT.SUCCESS.MESSAGE' | translate }}</p>
          <div class="success-actions">
            <button mat-raised-button color="primary" (click)="goToReports()" class="success-action-button">
              {{ 'AIEO.INPUT.SUCCESS.ACTION' | translate }}
            </button>
            <button mat-button (click)="dismissSuccessMessage()" class="dismiss-button">
              {{ 'AIEO.INPUT.SUCCESS.DISMISS' | translate }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="input-form-container" [@slideInOut] [class.hidden]="showSuccessMessage">
      <mat-card class="input-card">
        <mat-card-content>
          <form [formGroup]="auditForm" (ngSubmit)="onSubmit()" class="audit-form">
            <div class="form-field">
              <glxy-form-field class="full-width">
                <glxy-label>{{ 'AIEO.INPUT.FORM.BRAND_NAME.LABEL' | translate }}</glxy-label>
                <input
                  matInput
                  formControlName="brandName"
                  [placeholder]="'AIEO.INPUT.FORM.BRAND_NAME.PLACEHOLDER' | translate"
                  class="form-input"
                />
                <glxy-error *ngIf="auditForm.get('brandName')?.invalid && auditForm.get('brandName')?.touched">
                  {{ getErrorMessage('brandName') }}
                </glxy-error>
              </glxy-form-field>
            </div>

            <div class="form-field">
              <glxy-form-field class="full-width">
                <glxy-label>{{ 'AIEO.INPUT.FORM.WEBSITE.LABEL' | translate }}</glxy-label>
                <input
                  matInput
                  formControlName="websiteUrl"
                  [placeholder]="'AIEO.INPUT.FORM.WEBSITE.PLACEHOLDER' | translate"
                  class="form-input"
                />
                <glxy-error *ngIf="auditForm.get('websiteUrl')?.invalid && auditForm.get('websiteUrl')?.touched">
                  {{ getErrorMessage('websiteUrl') }}
                </glxy-error>
              </glxy-form-field>
            </div>

            <div class="form-actions">
              <button
                mat-raised-button
                color="primary"
                type="submit"
                [disabled]="auditForm.invalid || isSubmitting"
                class="submit-button"
                [class.processing]="isSubmitting"
              >
                <span *ngIf="!isSubmitting">{{ 'AIEO.INPUT.FORM.SUBMIT' | translate }}</span>
                <span *ngIf="isSubmitting">{{ 'AIEO.INPUT.FORM.PROCESSING' | translate }}</span>
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</app-page>
