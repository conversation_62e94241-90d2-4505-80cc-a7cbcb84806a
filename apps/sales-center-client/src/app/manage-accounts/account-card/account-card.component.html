<mat-card appearance="outlined" class="business-card">
  <mat-card-header>
    <mat-card-title>
      <mat-icon class="unread-icon" *ngIf="!business.isRead" color="warn" mat-inline>lens</mat-icon>
      <div class="hotness-flames">
        <mat-icon mat-inline svgIcon="flame" *ngFor="let i of business.flames"></mat-icon>
      </div>
      <a routerLink="{{ business.manageAccountUrl }}">
        {{ business.name }}
      </a>
      <app-active-products-popout
        class="products"
        *ngIf="showProducts$ | async"
        [partnerId]="business.partnerId"
        [marketId]="business.marketId"
        [accountGroupId]="business.accountGroupId"
      ></app-active-products-popout>
    </mat-card-title>
  </mat-card-header>

  <mat-card-content>
    <div>
      <div class="contact-info">
        <span [innerHTML]="business.formattedAddress"></span>
        <br />
        <span>
          {{ business.phoneNumber || '' | phone: business.country }}
        </span>
      </div>
      <ng-container [ngTemplateOutlet]="badges"></ng-container>
    </div>
    <div class="stats">
      <ng-container [ngTemplateOutlet]="stats"></ng-container>
    </div>
  </mat-card-content>
  <mat-card-footer class="footer">
    <ng-container [ngTemplateOutlet]="footer"></ng-container>
  </mat-card-footer>
</mat-card>

<ng-template #badges>
  <div class="badges">
    <glxy-badge [color]="'dark-grey-solid'" *ngIf="lifecycleStage$ | async as lifecycleStage">
      {{ lifecycleStage | translate }}
    </glxy-badge>

    <glxy-badge [color]="business.salesStatusColor">
      {{ business.salesStatus }}
    </glxy-badge>

    <glxy-badge color="grey" class="tag" *ngIf="business.salesPersonAction && showSalespersonAction">
      {{ business.salesPersonActionFormatted }}
    </glxy-badge>

    <glxy-badge color="green" class="tag" *ngIf="business.opportunitiesTagFormatted$ | async as oppTag">
      {{ oppTag }}
    </glxy-badge>
  </div>
</ng-template>

<ng-template #stats>
  <div class="stat-item">
    {{ 'COMMON.SALESPERSON_LABEL' | translate }}

    <ng-container *ngIf="!business.salesPersonExists">
      <span class="no-assignee">
        {{ 'COMMON.NONE_LABEL' | translate }}
      </span>
    </ng-container>
    <ng-container *ngIf="business.salesPersonExists">
      <span
        *ngIf="business.salesPersonName$ | async as name; else loadingSalesperson"
        class="fade-in account-info-data"
      >
        {{ name }}
      </span>
    </ng-container>
    <ng-template #loadingSalesperson>
      <glxy-loading-spinner [inline]="true"></glxy-loading-spinner>
    </ng-template>
  </div>
  <div class="stat-item">
    <span>
      {{ 'COMMON.ACCOUNT_ATTRIBUTES.LAST_CUSTOMER_ACTIVITY' | translate }}
    </span>
    <span class="account-info-data">
      {{ business.daysSinceLastCustomerActivity$ | async }}
    </span>
  </div>
  <div class="stat-item">
    <span>
      {{ 'COMMON.ACCOUNT_ATTRIBUTES.LAST_SALES_ACTIVITY' | translate }}
    </span>
    <span class="account-info-data">
      {{ business.daysSinceLastSalesActivity$ | async }}
    </span>
  </div>
  <div class="stat-item">
    <span>
      {{ 'COMMON.ACCOUNT_ATTRIBUTES.LAST_CONNECTED' | translate }}
    </span>
    <span class="account-info-data">
      {{ business.daysSinceLastConnected$ | async }}
    </span>
  </div>
</ng-template>

<ng-template #footer>
  <ng-container *ngIf="(mobile$ | async) === false; else mobileMenuView">
    <div>
      <ng-container
        [ngTemplateOutlet]="scoreCard"
        [ngTemplateOutletContext]="{ mobile: mobile$ | async }"
      ></ng-container>
      <ng-container
        [ngTemplateOutlet]="campaign"
        [ngTemplateOutletContext]="{ mobile: mobile$ | async }"
      ></ng-container>
      <ng-container [ngTemplateOutlet]="sendContactEmail"></ng-container>
      <ng-container
        [ngTemplateOutlet]="snapshotListing"
        [ngTemplateOutletContext]="{ mobile: mobile$ | async }"
      ></ng-container>
      <app-contacts-phone-button [business]="business"></app-contacts-phone-button>
      <ng-container
        [ngTemplateOutlet]="meetings"
        [ngTemplateOutletContext]="{ mobile: mobile$ | async }"
      ></ng-container>
      <ng-container
        interactable
        *ngIf="showInbox$ | async"
        [ngTemplateOutlet]="inbox"
        [ngTemplateOutletContext]="{ mobile: mobile$ | async }"
      ></ng-container>
    </div>
    <div>
      <a mat-button color="primary" (click)="openComposerClickEvent()">
        {{ 'SALES_ACTIVITY.LOG_CALL_EMAIL_MEETING' | translate }}
      </a>
    </div>
  </ng-container>
  <ng-template #mobileMenuView>
    <button mat-button color="primary" [matMenuTriggerFor]="mobileMenu">
      {{ 'COMMON.ACTION_LABELS.MORE_ACTIONS' | translate }}
      <mat-icon matSuffix>arrow_drop_down</mat-icon>
    </button>
    <mat-menu #mobileMenu="matMenu">
      <ng-container *ngIf="mobile$ | async as mobile">
        <ng-container [ngTemplateOutlet]="scoreCard" [ngTemplateOutletContext]="{ mobile: mobile }"></ng-container>
        <ng-container [ngTemplateOutlet]="campaign" [ngTemplateOutletContext]="{ mobile: mobile }"></ng-container>
        <ng-container
          [ngTemplateOutlet]="snapshotListing"
          [ngTemplateOutletContext]="{ mobile: mobile }"
        ></ng-container>
        <ng-container [ngTemplateOutlet]="meetings" [ngTemplateOutletContext]="{ mobile: mobile }"></ng-container>
        <ng-container
          interactable
          *ngIf="showInbox$ | async"
          [ngTemplateOutlet]="inbox"
          [ngTemplateOutletContext]="{ mobile: mobile }"
        ></ng-container>
      </ng-container>
      <button mat-menu-item (click)="openComposerClickEvent()">
        <mat-icon color="primary">insert_comment</mat-icon>
        {{ 'SALES_ACTIVITY.LOG_CALL_EMAIL_MEETING' | translate }}
      </button>
    </mat-menu>
  </ng-template>
</ng-template>

<ng-template #inbox let-mobile="mobile">
  <inbox-button-send-message
    [config]="{
      currentParticipant: currentIAMParticipant$ | async,
      subjectParticipants: subjectParticipants,
      channel: conversationChannel
    }"
    [type]="mobile ? 'menu' : 'icon'"
    [primaryIcon]="true"
  ></inbox-button-send-message>
</ng-template>

<ng-template #scoreCard let-mobile="mobile">
  <ng-container *ngIf="mobile === false; else mobileScoreCard">
    <ng-container *ngIf="business.latestSnapshotExpiry || (currentSnapshot$ | async); else add">
      <button
        mat-icon-button
        centralize-icon
        color="primary"
        (click)="openViewScorecard()"
        matTooltip="{{ 'SNAPSHOT_REPORT.VIEW_SNAPSHOT' | whitelabelTranslate | async }}"
      >
        <mat-icon color="primary">description</mat-icon>
      </button>
      <button
        mat-icon-button
        centralize-icon
        color="primary"
        [disabled]="(snapshotExpired$ | async) === false"
        (click)="onSnapshotRenewPressed()"
        matTooltip="{{ 'SNAPSHOT_REPORT.REFRESH_SNAPSHOT' | whitelabelTranslate | async }}"
      >
        <mat-icon color="primary">refresh</mat-icon>
      </button>
    </ng-container>
    <ng-template #add>
      <button
        mat-icon-button
        centralize-icon
        [disabled]="(exceedLimit$ | async) === true"
        color="primary"
        (click)="snapshotCreateClickEvent()"
        [matTooltip]="
          (exceedLimit$ | async) === false
            ? ('SNAPSHOT_REPORT.CREATE_SNAPSHOT' | whitelabelTranslate | async)
            : ('SNAPSHOT_REPORT.DISABLE_CREATE_SNAPSHOT' | translate)
        "
      >
        <mat-icon color="primary">note_add</mat-icon>
      </button>
    </ng-template>
  </ng-container>
  <ng-template #mobileScoreCard>
    <ng-container *ngIf="business.latestSnapshotExpiry || (currentSnapshot$ | async); else addMobile">
      <button
        mat-menu-item
        color="primary"
        (click)="openViewScorecard()"
        matTooltip="{{ 'SNAPSHOT_REPORT.VIEW_SNAPSHOT' | whitelabelTranslate | async }}"
      >
        <mat-icon color="primary">description</mat-icon>
        <span>{{ 'SNAPSHOT_REPORT.VIEW_SNAPSHOT' | whitelabelTranslate | async }}</span>
      </button>
      <button
        mat-menu-item
        color="primary"
        [disabled]="(snapshotExpired$ | async) === false"
        (click)="onSnapshotRenewPressed()"
        matTooltip="{{ 'SNAPSHOT_REPORT.REFRESH_SNAPSHOT' | whitelabelTranslate | async }}"
      >
        <mat-icon color="primary">refresh</mat-icon>
        <span>{{ 'SNAPSHOT_REPORT.REFRESH_SNAPSHOT' | whitelabelTranslate | async }}</span>
      </button>
    </ng-container>
    <ng-template #addMobile>
      <button
        mat-menu-item
        [disabled]="(exceedLimit$ | async) === true"
        color="primary"
        (click)="snapshotCreateClickEvent()"
        [matTooltip]="
          (exceedLimit$ | async) === false
            ? ('SNAPSHOT_REPORT.CREATE_SNAPSHOT' | whitelabelTranslate | async)
            : ('SNAPSHOT_REPORT.DISABLE_CREATE_SNAPSHOT' | translate)
        "
      >
        <mat-icon color="primary">note_add</mat-icon>
        <span>{{ 'SNAPSHOT_REPORT.CREATE_SNAPSHOT' | whitelabelTranslate | async }}</span>
      </button>
    </ng-template>
  </ng-template>
</ng-template>

<ng-template #campaign let-mobile="mobile">
  <ng-container *ngIf="mobile === false; else mobileCampaign">
    <button
      mat-icon-button
      centralize-icon
      color="primary"
      *ngIf="canSendCampaigns$ | async"
      (click)="campaignSendMail()"
      [matTooltip]="
        (business.snapshotOrEmailCampaignStatus
          ? 'ACCOUNT_DETAILS.CAMPAIGNS.SENT_CAMPAIGN'
          : 'ACCOUNT_DETAILS.CAMPAIGNS.SET_UP_CAMPAIGN'
        ) | translate
      "
    >
      <mat-icon color="primary">{{ business.snapshotOrEmailCampaignStatus ? 'mail_outline' : 'email' }}</mat-icon>
    </button>
  </ng-container>
</ng-template>
<ng-template #mobileCampaign>
  <button
    mat-menu-item
    color="primary"
    *ngIf="canSendCampaigns$ | async"
    (click)="campaignSendMail()"
    [matTooltip]="
      (business.snapshotOrEmailCampaignStatus
        ? 'ACCOUNT_DETAILS.CAMPAIGNS.SENT_CAMPAIGN'
        : 'ACCOUNT_DETAILS.CAMPAIGNS.SET_UP_CAMPAIGN'
      ) | translate
    "
  >
    <mat-icon color="primary">{{ business.snapshotOrEmailCampaignStatus ? 'mail_outline' : 'email' }}</mat-icon>
    <span>
      {{
        (business.snapshotOrEmailCampaignStatus
          ? 'ACCOUNT_DETAILS.CAMPAIGNS.SENT_CAMPAIGN'
          : 'ACCOUNT_DETAILS.CAMPAIGNS.SET_UP_CAMPAIGN'
        ) | translate
      }}
    </span>
  </button>
</ng-template>

<ng-template #sendContactEmail>
  <button
    mat-icon-button
    color="primary"
    [matMenuTriggerFor]="contactMenu"
    matTooltip="{{ 'COMMON.ACTION_LABELS.CONTACT_EMAILS' | translate }}"
  >
    <mat-icon>contact_mail</mat-icon>
  </button>
  <mat-menu #contactMenu="matMenu">
    <ng-template matMenuContent>
      <ng-container *ngIf="contacts$ | glxyAsyncStatus | async as obs" [ngSwitch]="obs.status">
        <ng-container *ngSwitchCase="'loaded'">
          <ng-container *ngTemplateOutlet="contactList; context: { $implicit: obs.value }"></ng-container>
        </ng-container>
        <ng-container *ngSwitchCase="'loading'">
          <glxy-loading-spinner [inline]="true" class="loading-spinner"></glxy-loading-spinner>
        </ng-container>
      </ng-container>
    </ng-template>
  </mat-menu>
</ng-template>

<ng-template #contactList let-contacts>
  <ng-container *ngIf="contacts?.length !== 0; else addContact">
    <ng-container *ngFor="let contact of contacts">
      <button
        mat-menu-item
        class="menu-item-align"
        *ngIf="contact.contactEmail"
        [copyToClipBoardAndAlert]="{ email: contact.contactEmail }"
      >
        <mat-icon class="menu-icon">file_copy</mat-icon>
        <span>
          {{ contact.fullName }}
          <a class="menu-link menu-button" [attr.href]="mailToContactEmailLink(contact.contactEmail)">
            {{ contact.contactEmail }}
          </a>
        </span>
      </button>
    </ng-container>
  </ng-container>
  <ng-template #addContact>
    <button mat-menu-item appAddContact [businessId]="business.accountGroupId">
      <mat-icon>add</mat-icon>
      {{ 'ACCOUNT_DETAILS.PAGE_MENU.ADD_CONTACT' | translate }}
    </button>
  </ng-template>
</ng-template>

<ng-template #snapshotListing let-mobile="mobile">
  <ng-container *ngIf="(displaySnapshotListingScan$ | async) && showSnapshotLite(business)">
    <ng-container *ngIf="mobile === false; else mobileSnapshotListing">
      <a
        mat-icon-button
        [href]="snapshotListingUrl(business)"
        target="_blank"
        matTooltip="{{ 'SNAPSHOT_REPORT.VIEW_LISTING_SCAN' | whitelabelTranslate | async }}"
      >
        <mat-icon svgIcon="listing-scan"></mat-icon>
      </a>
    </ng-container>
    <ng-template #mobileSnapshotListing>
      <a
        mat-menu-item
        [href]="snapshotListingUrl(business)"
        target="_blank"
        matTooltip="{{ 'SNAPSHOT_REPORT.VIEW_LISTING_SCAN' | whitelabelTranslate | async }}"
      >
        <mat-icon svgIcon="listing-scan"></mat-icon>
        <span>{{ 'SNAPSHOT_REPORT.VIEW_LISTING_SCAN' | whitelabelTranslate | async }}</span>
      </a>
    </ng-template>
  </ng-container>
</ng-template>

<ng-template #meetings let-mobile="mobile">
  <button
    *ngIf="mobile === false; else mobileMeetings"
    mat-icon-button
    color="primary"
    class="actionbar__button"
    [matMenuTriggerFor]="meetingsMenu"
    matTooltip="{{ 'INTEGRATIONS.BUTTON_TOOLTIP' | translate }}"
  >
    <mat-icon>insert_invitation</mat-icon>
  </button>
  <mat-menu #meetingsMenu="matMenu">
    <ng-template matMenuContent>
      <ng-container *ngIf="contacts$ | glxyAsyncStatus | async as obs" [ngSwitch]="obs.status">
        <ng-container *ngSwitchCase="'loaded'">
          <ng-container *ngTemplateOutlet="meetingOptionsList"></ng-container>
        </ng-container>
        <ng-container *ngSwitchCase="'loading'">
          <glxy-loading-spinner [inline]="true" class="loading-spinner"></glxy-loading-spinner>
        </ng-container>
      </ng-container>
    </ng-template>
  </mat-menu>

  <ng-template #mobileMeetings>
    <button *ngIf="zoomEnabled" mat-menu-item integrationsZoomInstantMeeting [config]="meetingConfig$ | async">
      <mat-icon svgIcon="zoom-icon"></mat-icon>
      {{ 'INTEGRATIONS.MEETING_INFO_DIALOG.ZOOM_CREATE' | translate }}
    </button>
    <button *ngIf="meetEnabled" mat-menu-item integrationsGoogleMeetInstantMeeting [config]="meetingConfig$ | async">
      <mat-icon svgIcon="meet-icon"></mat-icon>
      {{ 'INTEGRATIONS.MEETING_INFO_DIALOG.MEET_CREATE' | translate }}
    </button>
  </ng-template>
</ng-template>

<ng-template #meetingOptionsList>
  <button *ngIf="zoomEnabled" mat-menu-item integrationsZoomInstantMeeting [config]="meetingConfig$ | async">
    <mat-icon svgIcon="zoom-icon"></mat-icon>
    {{ 'INTEGRATIONS.MEETING_INFO_DIALOG.ZOOM_CREATE' | translate }}
  </button>
  <button *ngIf="meetEnabled" mat-menu-item integrationsGoogleMeetInstantMeeting [config]="meetingConfig$ | async">
    <mat-icon svgIcon="meet-icon"></mat-icon>
    {{ 'INTEGRATIONS.MEETING_INFO_DIALOG.MEET_CREATE' | translate }}
  </button>
</ng-template>

<ng-template #openScorecard>
  <app-snapshot-scorecard
    [businessId]="business.accountGroupId"
    [snapshotName]="snapshotName$ | async"
    [trigger]="triggerUpdate$ | async"
    [trackingCategory]="'manage-accounts-snapshot'"
    (refreshReportClickedEvent)="onSnapshotRenewPressed()"
    (closeSlideOutPanel)="closeDrawer()"
  ></app-snapshot-scorecard>
</ng-template>
