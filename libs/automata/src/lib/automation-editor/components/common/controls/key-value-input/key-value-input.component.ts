import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { FormArray, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { ALL_VARIABLE_MENU_DATA_TYPES } from '../../../../../common/constants';
import { AutomationVariableMenuComponent } from '../../../../../common/components';
import { insertVariable } from '../../../../../common/components/automation-variable-menu/automation-variable-menu.service';
import { v4 as uuidv4 } from 'uuid';

export interface KeyValueForm {
  key: FormControl<string>;
  value: FormControl<string>;
}

export interface KeyValue {
  key: string;
  value: string;
}

@Component({
  selector: 'automata-key-value-input',
  templateUrl: './key-value-input.component.html',
  styleUrls: ['./key-value-input.component.scss'],
  imports: [
    CommonModule,
    TranslateModule,
    GalaxyAlertModule,
    MatButtonModule,
    MatCardModule,
    FormsModule,
    GalaxyFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    MatIconModule,
    MatSelectModule,
    AutomationVariableMenuComponent,
    AutomationVariableMenuComponent,
  ],
})
export class KeyValueInputComponent {
  public readonly allDataTypes = ALL_VARIABLE_MENU_DATA_TYPES;
  @Input() formArray: FormArray<FormGroup<KeyValueForm>>;
  @Input() keyLabel = 'AUTOMATIONS.EDITOR.TASKS.TRIGGER_WEBHOOK.PANEL.FIELD';
  @Input() valueLabel = 'AUTOMATIONS.EDITOR.TASKS.TRIGGER_WEBHOOK.PANEL.VALUE';
  @Input() title = '';
  @Input() useTextArea = false;
  public id = uuidv4();

  removeField(i: number): void {
    this.formArray.removeAt(i);
  }

  addField(): void {
    this.formArray.push(
      new FormGroup<KeyValueForm>({
        key: new FormControl<string>(''),
        value: new FormControl<string>(''),
      }),
    );
  }

  insertVariable(elementID: string, control: any, variable: string): void {
    insertVariable(elementID, control, variable);
  }
}
