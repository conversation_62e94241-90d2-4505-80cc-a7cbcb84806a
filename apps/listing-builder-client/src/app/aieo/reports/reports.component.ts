import { Component, OnInit, ChangeDetectionStrategy, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { Mat<PERSON>ard, MatCardContent, Mat<PERSON>ardHeader, MatCardTitle } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatListModule } from '@angular/material/list';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { SharedModule } from '../../shared/shared.module';
import { IconComponent } from '@vendasta/uikit';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { ReportsService } from '../reports/reports.service';
import { AuditReport } from '../reports/reports.interface';
import { Observable, take } from 'rxjs';
import { AGIDTOKEN } from '../../app.module';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-reports',
  templateUrl: './reports.component.html',
  styleUrls: ['./reports.component.scss'],
  changeDetection: ChangeDetectionStrategy.Default,
  imports: [
    CommonModule,
    MatCard,
    MatCardContent,
    MatCardHeader,
    MatCardTitle,
    SharedModule,
    IconComponent,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatListModule,
    MatDividerModule,
    MatProgressBarModule,
    GalaxyBadgeModule,
    TranslateModule,
  ],
  standalone: true,
})
export class ReportsComponent implements OnInit {
  reports: AuditReport[] = [];
  loading = false;

  constructor(
    private reportsService: ReportsService,
    private router: Router,
    private route: ActivatedRoute,
    @Inject(AGIDTOKEN) private accountGroupId$: Observable<string>,
  ) {}

  ngOnInit(): void {
    this.loadReports();
  }

  loadReports(): void {
    this.loading = true;
    this.reportsService.getReports().subscribe({
      next: (reports) => {
        this.reports = reports;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading reports:', error);
        this.loading = false;
      },
    });
  }

  onViewReport(report: AuditReport): void {
    if (report.status === 'completed') {
      this.reportsService.setCurrentReport(report);

      this.accountGroupId$.pipe(take(1)).subscribe((accountGroupId) => {
        this.router.navigate([`/edit/account/${accountGroupId}/app/aieo/results`]);
      });
    }
  }

  onRunNewAudit(): void {
    this.accountGroupId$.pipe(take(1)).subscribe((accountGroupId) => {
      this.router.navigate([`/edit/account/${accountGroupId}/app/aieo`]);
    });
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'completed':
        return 'accent';
      case 'in_progress':
        return 'warn';
      case 'failed':
        return 'error';
      default:
        return 'primary';
    }
  }

  getStatusBadgeColor(status: string): string {
    switch (status) {
      case 'completed':
        return 'green';
      case 'in_progress':
        return 'blue';
      case 'failed':
        return 'red';
      default:
        return 'grey';
    }
  }

  getStatusIcon(status: string): string {
    switch (status) {
      case 'completed':
        return 'check_circle';
      case 'in_progress':
        return 'hourglass_empty';
      case 'failed':
        return 'error';
      default:
        return 'pending';
    }
  }

  trackByReportId(index: number, report: AuditReport): string {
    return report.id;
  }
}
