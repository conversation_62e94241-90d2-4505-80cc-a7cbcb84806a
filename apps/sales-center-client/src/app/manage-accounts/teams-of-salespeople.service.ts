import { Observable, of } from 'rxjs';
import { Inject, Injectable } from '@angular/core';
import { SALES_TEAMS_TOKEN, SalesTeam, SalesTeamsService } from '../sales-teams';
import { take, tap } from 'rxjs/operators';

/*TODO: This class should be refactored to only load teams when they are needed and then cache them in some way rather than loading
 *       all teams when only one needs to be checked. */
@Injectable()
export class TeamsOfSalespeopleService {
  salesTeamMembers = new Map<string, string[]>();

  constructor(
    @Inject(SALES_TEAMS_TOKEN) readonly salesTeams$: Observable<SalesTeam[]>,
    private readonly salesTeamService: SalesTeamsService,
  ) {
    this.salesTeams$.pipe(take(1)).subscribe((r) =>
      r.map((e) =>
        this.getSalesTeamMembers(e.groupId)
          .pipe(
            take(1),
            tap((team) => this.salesTeamMembers.set(e.groupId, team)),
          )
          .subscribe(),
      ),
    );
  }

  private getSalesTeamMembers(groupId: string): Observable<string[]> {
    return this.salesTeamService.listSalesTeamMembers(groupId);
  }

  getMembers(groupId: string): Observable<string[]> {
    return of(this.salesTeamMembers.get(groupId));
  }
}
