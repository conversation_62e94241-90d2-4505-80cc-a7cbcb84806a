/* tslint:disable */
import { SalesStatusFilterId } from './manage-accounts-filters';
import { SalesPersonAction } from '../account-history/account-history.component';
import { BusinessSearchResponse } from '@vendasta/sales';
import { BusinessInterface } from '@vendasta/sales/lib/_internal';

export const mockBiz1 = <BusinessInterface>{
  accountGroupId: 'AG-5RRLBLBFGL',
  partnerId: 'VUNI',
  marketId: 'default',
  name: "<PERSON>'s burgers",
  hotness: 3,
  city: 'Saskatoon',
  zip: 'S7N2E4',
  address: "125 Bob's St",
  state: 'SK',
  country: 'Canada',
  phoneNumber: '(*************',
  salespersonId: 'UID-123',
  lastSalesActivityDate: new Date('2018-10-12T14:46:26Z'),
  lastCustomerActivityDate: new Date('2018-10-12T14:45:22.000Z'),
  salesStatus: SalesStatusFilterId.FOLLOW_UP_NEEDED,
  salesStatusTag: SalesStatusFilterId.FOLLOW_UP_NEEDED,
  isRead: false,
  salesPersonAction: SalesPersonAction.EMAIL_SENT,
  latestSnapshotExpiry: new Date('2018-10-19T14:45:41Z'),
  snapshotOrCampaignEmailStatus: false,
  activityType: null,
  lastConnectedDate: null,
};

export const mockBiz2 = <BusinessInterface>{
  accountGroupId: 'AG-6CHBZWSR68',
  partnerId: 'VUNI',
  marketId: 'default',
  name: "Bob's burgers",
  hotness: 0,
  city: 'Saskatoon',
  zip: 'S7N2E4',
  address: "123 Bob's St",
  state: 'SK',
  country: 'Canada',
  phoneNumber: '(*************',
  salespersonId: 'UID-456',
  lastSalesActivityDate: new Date('2018-10-11T17:07:27Z'),
  lastCustomerActivityDate: new Date('2018-10-11T17:04:50.000Z'),
  salesStatus: SalesStatusFilterId.IN_PROGRESS,
  salesStatusTag: SalesStatusFilterId.IN_PROGRESS,
  isRead: false,
  salesPersonAction: SalesPersonAction.EMAIL_SENT,
  latestSnapshotExpiry: new Date('2018-10-18T17:05:01Z'),
  snapshotOrCampaignEmailStatus: false,
  activityType: null,
  lastConnectedDate: new Date(),
};

export const mockSearchBusinessResponse = <BusinessSearchResponse>{
  pagingMetadata: {
    nextCursor: '',
    hasMore: false,
    totalResults: 2,
  },
  businesses: [mockBiz1, mockBiz2],
};

export const mockBusinesses = [mockBiz1, mockBiz2];
