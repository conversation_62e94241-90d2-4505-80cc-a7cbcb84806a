import { SalesTeamInterface, Salesperson } from '@galaxy/types';
import { JestScheduler, schedule } from '@vendasta/rx-utils';
import { FilterField } from '@vendasta/uikit';
import { EMPTY, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { TestScheduler } from 'rxjs/testing';
import { LifecycleStageFilterOption } from '../manage-accounts-filters-lifecycle';
import { Taxonomy } from '../manage-accounts-filters-taxonomy';
import { AccountGoal, AccountTag } from '../manage-accounts.service';
import { FilterOptions, FilterSelection, ManageAccountsFiltersService } from './manage-accounts-filters.service';

const globalSalesperson = of('SP-123');
const globalSalesTeams = of([]);
const globalSalesPeople = of([]);
const globalAccountTags = of([]);
const globalGoals = of([]);
const globalBusinessCategories = of([]);
const globalLifecycleStages = of([]);

let sched: TestScheduler;

describe('ManageAccountsFiltersService', () => {
  beforeEach(() => {
    sched = new JestScheduler();
  });
  afterEach(() => sched.flush());
  describe('filterOptions$', () => {
    it('should not emit anything if none of the sources are ready', () => {
      const loggedInSalesperson$ = EMPTY;
      const s = new ManageAccountsFiltersService(
        loggedInSalesperson$,
        globalSalesPeople,
        globalSalesTeams,
        globalAccountTags,
        globalGoals,
        globalBusinessCategories,
        globalLifecycleStages,
      );
      sched.expectObservable(s.filterOptions$).toBe('|');
    });
    it('should emit the logged in salesperson for selection', () => {
      const salespersonId = 'SP-123';
      const loggedInSalesperson$ = sched.createColdObservable('--x', {
        x: salespersonId,
      });
      const expectedValue: FilterOptions = {
        goals: [],
        accountTags: [],
        businessCategories: [],
        salesPeople: [],
        salesTeams: [],
        selectedSalesperson: 'SP-123',
        lifecycleStages: [],
      } as FilterOptions;
      const s = new ManageAccountsFiltersService(
        loggedInSalesperson$,
        globalSalesPeople,
        globalSalesTeams,
        globalAccountTags,
        globalGoals,
        globalBusinessCategories,
        globalLifecycleStages,
      );
      sched.expectObservable(s.filterOptions$).toBe('--x', {
        x: expectedValue,
      });
    });
    it('should populate salesTeams filter option', () => {
      const salesTeams = <SalesTeamInterface[]>[
        {
          teamName: 'ST-1',
          groupId: 'gp-1',
          marketId: 'mk-1',
          partnerId: 'p-1',
        },
      ];
      const expectedValue: FilterOptions = {
        salesTeams: <SalesTeamInterface[]>[{ teamName: 'ST-1', groupId: 'gp-1', marketId: 'mk-1', partnerId: 'p-1' }],
        goals: [],
        accountTags: [],
        salesPeople: [],
        businessCategories: [],
        selectedSalesperson: 'SP-123',
        lifecycleStages: [],
      } as FilterOptions;
      const salesTeams$ = sched.createColdObservable('-x', {
        x: salesTeams,
      });
      const s = new ManageAccountsFiltersService(
        globalSalesperson,
        globalSalesPeople,
        salesTeams$,
        globalAccountTags,
        globalGoals,
        globalBusinessCategories,
        globalLifecycleStages,
      );

      sched.expectObservable(s.filterOptions$).toBe('-x', { x: expectedValue });
    });
    it('should populate accountTags filter option', () => {
      const accountTags: AccountTag[] = [
        { name: 'TG-123', value: 'val-123' },
        {
          name: 'TG-456',
          value: 'val-456',
        },
        { name: 'TG-789', value: 'val-789' },
      ];
      const expectedValue: FilterOptions = {
        accountTags: [
          { name: 'TG-123', value: 'val-123' },
          { name: 'TG-456', value: 'val-456' },
          {
            name: 'TG-789',
            value: 'val-789',
          },
        ],
        goals: [],
        salesPeople: [],
        salesTeams: [],
        selectedSalesperson: 'SP-123',
        businessCategories: [],
        lifecycleStages: [],
      } as FilterOptions;
      const accountTags$ = sched.createColdObservable('-x', {
        x: accountTags,
      });
      const s = new ManageAccountsFiltersService(
        globalSalesperson,
        globalSalesPeople,
        globalSalesTeams,
        accountTags$,
        globalGoals,
        globalBusinessCategories,
        globalLifecycleStages,
      );

      sched.expectObservable(s.filterOptions$).toBe('-x', { x: expectedValue });
    });
    it('should populate goals filter option', () => {
      const goals: AccountGoal[] = [{ value: 'GL-1' }, { value: 'GL-2' }, { value: 'GL-3' }] as AccountGoal[];
      const expectedValue: FilterOptions = {
        goals: [{ value: 'GL-1' }, { value: 'GL-2' }, { value: 'GL-3' }],
        accountTags: [],
        salesPeople: [],
        salesTeams: [],
        businessCategories: [],
        selectedSalesperson: 'SP-123',
        lifecycleStages: [],
      } as FilterOptions;
      const goals$ = sched.createColdObservable('-x', {
        x: goals,
      });
      const s = new ManageAccountsFiltersService(
        globalSalesperson,
        globalSalesPeople,
        globalSalesTeams,
        globalAccountTags,
        goals$,
        globalBusinessCategories,
        globalLifecycleStages,
      );

      sched.expectObservable(s.filterOptions$).toBe('-x', { x: expectedValue });
    });
    it('should populate salesPeople filter option', () => {
      const salesPeople = <Salesperson[]>[{ fullName: 'Jimmy Smitts', id: 'SP-123' }];

      const expectedValue: FilterOptions = {
        salesPeople: <Salesperson[]>[{ fullName: 'Jimmy Smitts', id: 'SP-123' }],
        goals: [],
        accountTags: [],
        salesTeams: [],
        businessCategories: [],
        selectedSalesperson: 'SP-123',
        lifecycleStages: [],
      } as FilterOptions;
      const salesPeople$ = sched.createColdObservable('-x', {
        x: salesPeople,
      });
      const s = new ManageAccountsFiltersService(
        globalSalesperson,
        salesPeople$,
        globalSalesTeams,
        globalAccountTags,
        globalGoals,
        globalBusinessCategories,
        globalLifecycleStages,
      );
      sched.expectObservable(s.filterOptions$).toBe('-x', { x: expectedValue });
    });

    it('should populate the lifecycle stage filter options', () => {
      const lifecycleFilterOptions: LifecycleStageFilterOption[] = [
        { id: 0, name: 'Unset' },
        { id: 1, name: 'Lead' },
        { id: 2, name: 'Prospect' },
        { id: 3, name: 'Customer' },
      ];
      const expectedFilterOptions: FilterOptions = {
        goals: [],
        accountTags: [],
        salesPeople: [],
        salesTeams: [],
        businessCategories: [],
        selectedSalesperson: 'SP-123',
        lifecycleStages: [
          { id: 0, name: 'Unset' },
          { id: 1, name: 'Lead' },
          { id: 2, name: 'Prospect' },
          { id: 3, name: 'Customer' },
        ],
      } as FilterOptions;
      const lifecycleStages$ = sched.createColdObservable('-x', {
        x: lifecycleFilterOptions,
      });
      const svc = new ManageAccountsFiltersService(
        globalSalesperson,
        globalSalesPeople,
        globalSalesTeams,
        globalAccountTags,
        globalGoals,
        globalBusinessCategories,
        lifecycleStages$,
      );

      sched.expectObservable(svc.filterOptions$).toBe('-x', { x: expectedFilterOptions });
    });

    it('should populate business categories filter option', () => {
      const businessCategories = <Taxonomy[]>[
        { id: 'tax-1', name: 'taxonomy name 1' },
        {
          id: 'tax-2',
          name: 'taxonomy name 2',
        },
      ];

      const expectedValue: FilterOptions = {
        salesPeople: [],
        goals: [],
        accountTags: [],
        salesTeams: [],
        businessCategories: businessCategories,
        selectedSalesperson: 'SP-123',
        lifecycleStages: [],
      } as FilterOptions;
      const busCats$ = sched.createColdObservable('x', {
        x: businessCategories,
      });
      const s = new ManageAccountsFiltersService(
        globalSalesperson,
        globalSalesPeople,
        globalSalesTeams,
        globalAccountTags,
        globalGoals,
        busCats$,
        globalLifecycleStages,
      );
      sched.expectObservable(s.filterOptions$).toBe('x', { x: expectedValue });
    });
  });
  describe('filters$', () => {
    it('should accumulate all of our filter events with throttling', () => {
      const eventArray: FilterField<any>[] = [
        { name: 'testfield1' },
        { name: 'testfield2' },
        { name: 'testfield3' },
        { name: 'testfield4' },
      ] as FilterField<any>[];
      const delayTime = sched.createTime('---|');
      const s = new ManageAccountsFiltersService(
        globalSalesperson,
        globalSalesPeople,
        globalSalesTeams,
        globalAccountTags,
        globalGoals,
        globalBusinessCategories,
        globalLifecycleStages,
        sched,
        delayTime,
      );

      schedule(sched, '|', () => s.pushEvent(eventArray[0]));
      schedule(sched, '-|', () => s.pushEvent(eventArray[1]));
      schedule(sched, '--|', () => s.pushEvent(eventArray[2]));
      schedule(sched, '---|', () => s.pushEvent(eventArray[3]));
      sched.expectObservable(s.filters$).toBe('---x', { x: expect.anything() });
    });
    it('should accumulate the correct values', () => {
      const eventArray: FilterField<any>[] = [
        { name: 'testfield1', id: 'key1', value: 1 },
        { name: 'testfield2', id: 'key2', value: 2 },
        { name: 'testfield3', id: 'key3', value: 3 },
        { name: 'testfield4', id: 'key4', value: 4 },
      ] as FilterField<any>[];
      const delayTime = sched.createTime('---|');
      const s = new ManageAccountsFiltersService(
        globalSalesperson,
        globalSalesPeople,
        globalSalesTeams,
        globalAccountTags,
        globalGoals,
        globalBusinessCategories,
        globalLifecycleStages,
        sched,
        delayTime,
      );

      schedule(sched, '|', () => s.pushEvent(eventArray[0]));
      schedule(sched, '-|', () => s.pushEvent(eventArray[1]));
      schedule(sched, '--|', () => s.pushEvent(eventArray[2]));
      schedule(sched, '---|', () => s.pushEvent(eventArray[3]));

      const expectedValue: FilterSelection[] = [
        { filterFieldName: 'testfield1', key: 'key1', value: 1 },
        { filterFieldName: 'testfield2', key: 'key2', value: 2 },
        { filterFieldName: 'testfield3', key: 'key3', value: 3 },
        { filterFieldName: 'testfield4', key: 'key4', value: 4 },
      ];
      sched.expectObservable(s.filters$).toBe('---x', {
        x: expectedValue,
      });
    });
    it('should accumulate the correct values while updating duplicate filter keys, and with correct order', () => {
      const eventArray = [
        { name: 'testfield1', id: 'key1', value: 1 },
        { name: 'testfield2', id: 'key2', value: 2 },
        { name: 'testfield3', id: 'key1', value: 3 },
        { name: 'testfield4', id: 'key4', value: 4 },
      ] as FilterField<any>[];
      const delayTime = sched.createTime('---|');
      const s = new ManageAccountsFiltersService(
        globalSalesperson,
        globalSalesPeople,
        globalSalesTeams,
        globalAccountTags,
        globalGoals,
        globalBusinessCategories,
        globalLifecycleStages,
        sched,
        delayTime,
      );

      schedule(sched, '|', () => s.pushEvent(eventArray[0]));
      schedule(sched, '-|', () => s.pushEvent(eventArray[1]));
      schedule(sched, '--|', () => s.pushEvent(eventArray[2]));
      schedule(sched, '---|', () => s.pushEvent(eventArray[3]));

      const expectedValue: FilterSelection[] = [
        { filterFieldName: 'testfield2', key: 'key2', value: 2 },
        { filterFieldName: 'testfield3', key: 'key1', value: 3 },
        { filterFieldName: 'testfield4', key: 'key4', value: 4 },
      ];
      sched.expectObservable(s.filters$).toBe('---x', {
        x: expectedValue,
      });
    });
    it('should accumulate all of our filter events with throttling with an extra event afterward', () => {
      const eventArray = [
        { name: 'testfield1', id: 'key1', value: 1 },
        { name: 'testfield2', id: 'key2', value: 2 },
        { name: 'testfield3', id: 'key3', value: 3 },
        { name: 'testfield4', id: 'key4', value: 4 },
        { name: 'testfield5', id: 'key5', value: 5 },
      ] as FilterField<any>[];
      const delayTime = sched.createTime('---|');
      const s = new ManageAccountsFiltersService(
        globalSalesperson,
        globalSalesPeople,
        globalSalesTeams,
        globalAccountTags,
        globalGoals,
        globalBusinessCategories,
        globalLifecycleStages,
        sched,
        delayTime,
      );
      const filtersLength = s.filters$.pipe(map((val) => Object.keys(val).length));

      schedule(sched, '|', () => s.pushEvent(eventArray[0]));
      schedule(sched, '-|', () => s.pushEvent(eventArray[1]));
      schedule(sched, '--|', () => s.pushEvent(eventArray[2]));
      schedule(sched, '---|', () => s.pushEvent(eventArray[3]));
      schedule(sched, '----|', () => s.pushEvent(eventArray[4]));
      sched.expectObservable(filtersLength).toBe('---x---y', { x: 4, y: 5 });
    });
  });
});
