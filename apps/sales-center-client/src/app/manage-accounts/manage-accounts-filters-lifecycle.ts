import { TranslateService } from '@ngx-translate/core';
import { Observable } from 'rxjs';
import { LifecycleStage } from '@vendasta/sales';
import { map } from 'rxjs/operators';

export const LIFECYCLE_OPTIONS_TOKEN = 'LIFECYCLE_OPTIONS_TOKEN';

export interface LifecycleStageFilterOption {
  id: LifecycleStage;
  name: string;
}

const lifecycleStageList = new Map<number, string>([
  [LifecycleStage.LIFECYCLE_STAGE_LEAD, 'COMMON.LIFECYCLE_STAGES.LEAD'],
  [LifecycleStage.LIFECYCLE_STAGE_PROSPECT, 'COMMON.LIFECYCLE_STAGES.PROSPECT'],
  [LifecycleStage.LIFECYCLE_STAGE_CUSTOMER, 'COMMON.LIFECYCLE_STAGES.CUSTOMER'],
]);

export function getLifecycleStageFilterOptions(translate: TranslateService): Observable<LifecycleStageFilterOption[]> {
  const translationKeys = [...lifecycleStageList.values()];

  return translate.stream(translationKeys).pipe(
    map((translations) => {
      return [...lifecycleStageList.entries()].map(([id, key]) => {
        return <LifecycleStageFilterOption>{
          id: id,
          name: translations[key],
        };
      });
    }),
  );
}
