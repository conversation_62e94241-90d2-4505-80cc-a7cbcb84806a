import { Component, ChangeDetectionStrategy, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { Mat<PERSON>ard, MatCardContent } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { ReactiveFormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { SharedModule } from '../shared/shared.module';
import { IconComponent } from '@vendasta/uikit';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { AuditData } from './audit-results/audit.interface';
import { AuditDataService } from './audit-data.service';
import { ReportsService } from './reports/reports.service';
import { Subscription } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { TranslateModule } from '@ngx-translate/core';
import { trigger, state, style, transition, animate } from '@angular/animations';

@Component({
  selector: 'app-aieo-input',
  templateUrl: './aieo-input.component.html',
  styleUrls: ['./aieo-input.component.scss'],
  changeDetection: ChangeDetectionStrategy.Default,
  animations: [
    trigger('fadeInOut', [
      state(
        'void',
        style({
          opacity: 0,
          transform: 'translateY(-20px)',
        }),
      ),
      state(
        '*',
        style({
          opacity: 1,
          transform: 'translateY(0)',
        }),
      ),
      transition('void <=> *', [animate('0.3s ease-in-out')]),
    ]),
    trigger('slideInOut', [
      state(
        'void',
        style({
          opacity: 0,
          transform: 'translateY(20px)',
        }),
      ),
      state(
        '*',
        style({
          opacity: 1,
          transform: 'translateY(0)',
        }),
      ),
      transition('void <=> *', [animate('0.3s ease-in-out')]),
    ]),
  ],
  imports: [
    CommonModule,
    MatCard,
    SharedModule,
    MatCardContent,
    IconComponent,
    MatButtonModule,
    MatInputModule,
    ReactiveFormsModule,
    GalaxyLoadingSpinnerModule,
    GalaxyFormFieldModule,
    TranslateModule,
  ],
  standalone: true,
})
export class AieoInputComponent implements OnInit, OnDestroy {
  auditForm: FormGroup;
  isSubmitting = false;
  showSuccessMessage = false;
  auditData: AuditData | null = null;
  private websiteUrlSubscription: Subscription | null = null;
  private isAutoPrepending = false;
  private autoDismissTimer = null;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private auditDataService: AuditDataService,
    private reportsService: ReportsService,
    private dialog: MatDialog,
    private translateService: TranslateService,
  ) {
    this.auditForm = this.fb.group({
      brandName: ['', [Validators.required, Validators.minLength(2)]],
      websiteUrl: ['', [Validators.required, this.urlValidator()]],
    });
  }

  ngOnInit(): void {
    this.setupWebsiteUrlAutoPrepend();
  }

  ngOnDestroy(): void {
    if (this.websiteUrlSubscription) {
      this.websiteUrlSubscription.unsubscribe();
    }
    this.clearAutoDismissTimer();
  }

  private setupWebsiteUrlAutoPrepend(): void {
    const websiteUrlControl = this.auditForm.get('websiteUrl');
    if (websiteUrlControl) {
      this.websiteUrlSubscription = websiteUrlControl.valueChanges.subscribe((value: string) => {
        if (this.isAutoPrepending) {
          return;
        }

        if (value && typeof value === 'string') {
          const trimmedValue = value.trim();

          if (trimmedValue && !trimmedValue.match(/^https?:\/\//i)) {
            if (trimmedValue.includes('.') || /^[a-zA-Z0-9-]+$/.test(trimmedValue)) {
              this.isAutoPrepending = true;
              const newValue = `https://${trimmedValue}`;
              websiteUrlControl.setValue(newValue, { emitEvent: false });
              this.isAutoPrepending = false;
            }
          }
        }
      });
    }
  }

  urlValidator() {
    return (control: any) => {
      const url = control.value;
      if (!url) {
        return null;
      }
      return this.isValidUrl(url) ? null : { invalidUrl: true };
    };
  }

  private isValidUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return this.validateUrlComponents(urlObj, url);
    } catch (error) {
      return false;
    }
  }

  private validateUrlComponents(urlObj: URL, originalUrl: string): boolean {
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return false;
    }

    const hostname = urlObj.hostname;
    if (!hostname || hostname.length > 253) {
      return false;
    }

    if (!this.isValidDomain(hostname)) {
      return false;
    }

    if (urlObj.pathname.length > 2048) {
      return false;
    }

    if (originalUrl.length > 2048) {
      return false;
    }

    return true;
  }

  private isValidDomain(hostname: string): boolean {
    const cleanHostname = hostname.replace(/^\.+|\.+$/g, '');

    if (!cleanHostname) {
      return false;
    }

    const labels = cleanHostname.split('.');

    if (labels.length > 127) {
      return false;
    }

    for (const label of labels) {
      if (!this.isValidLabel(label)) {
        return false;
      }
    }

    const tld = labels[labels.length - 1];
    if (tld.length < 2 || tld.length > 63) {
      return false;
    }

    return true;
  }

  private isValidLabel(label: string): boolean {
    if (label.length < 1 || label.length > 63) {
      return false;
    }

    if (!/^[a-z0-9]/i.test(label) || !/[a-z0-9]$/i.test(label)) {
      return false;
    }

    if (!/^[a-z0-9-]+$/i.test(label)) {
      return false;
    }

    if (label.includes('--')) {
      return false;
    }

    return true;
  }

  async onSubmit(): Promise<void> {
    if (this.auditForm.valid) {
      try {
        this.isSubmitting = true;
        this.auditData = null;

        const formData = this.auditForm.value;

        const reportId = this.reportsService.addReport(formData.brandName, formData.websiteUrl);

        this.reportsService.updateReportStatus(reportId, 'in_progress');

        this.showSuccessMessage = true;
        this.isSubmitting = false;
        this.clearFormFields();

        // Start auto-dismiss timer
        this.startAutoDismissTimer();

        this.processAuditInBackground(formData, reportId);
      } catch (error) {
        console.error('Error in form submission:', error);
        this.isSubmitting = false;
      }
    } else {
      this.markFormGroupTouched();
    }
  }

  private async processAuditInBackground(formData: any, reportId: string): Promise<void> {
    try {
      const totalSteps = 5;
      for (let i = 1; i <= totalSteps; i++) {
        await new Promise((resolve) => setTimeout(resolve, 400));
        const progress = Math.round((i / totalSteps) * 100);
        this.reportsService.updateReportProgress(reportId, progress);
      }

      this.generateMockAuditData(formData);

      if (this.auditData) {
        this.reportsService.updateReportStatus(reportId, 'completed', this.auditData);
        this.auditDataService.setAuditData(this.auditData);
      }
    } catch (error) {
      console.error('Error in processAuditInBackground:', error);
      this.reportsService.updateReportStatus(reportId, 'failed');
    }
  }

  generateMockAuditData(formData: any): void {
    const websiteUrl = formData.websiteUrl || '';

    const structuredDataScore = websiteUrl.includes('schema') ? 80 : Math.floor(Math.random() * 30) + 20;
    const schemaScore = websiteUrl.includes('json') ? 90 : Math.floor(Math.random() * 70) + 15;
    const robotsScore = websiteUrl.includes('robots') ? 100 : Math.floor(Math.random() * 40) + 30;
    const metadataScore = Math.floor(Math.random() * 60) + 20;
    const contentScore = Math.floor(Math.random() * 50) + 25;
    const pageLoadScore = Math.floor(Math.random() * 40) + 30;

    const overallScore = Math.round(
      (structuredDataScore + schemaScore + robotsScore + metadataScore + contentScore + pageLoadScore) / 6,
    );

    this.auditData = {
      overallScore: overallScore || 0,
      description: `Analysis of ${formData.brandName || 'your business'} website (${formData.websiteUrl || 'your website'}) shows ${overallScore < 50 ? 'significant room for improvement' : overallScore < 75 ? 'good potential with some enhancements' : 'strong AI visibility'} in AI search engine optimization. The site ${overallScore < 50 ? 'needs' : 'could benefit from'} improvements in structured data, metadata, and content optimization to better serve AI-powered search engines.`,
      sections: [
        {
          title: 'Structured Data',
          content:
            structuredDataScore > 50
              ? 'Good implementation of structured data found. Consider adding more specific business schema markup.'
              : 'Implement JSON-LD structured data to help AI systems understand your business type and services',
          score: structuredDataScore || 0,
        },
        {
          title: 'Schema Markup',
          content:
            schemaScore > 60
              ? ['Schema.org markup is well implemented', 'Consider adding more specific service schemas']
              : [
                  this.translateService.instant('AIEO.CONTENT.SCHEMA.ADD_LOCAL_BUSINESS'),
                  this.translateService.instant('AIEO.CONTENT.SCHEMA.ADD_REVIEWS'),
                ],
          score: schemaScore || 0,
        },
        {
          title: 'Robots.txt',
          content:
            robotsScore > 50
              ? this.translateService.instant('AIEO.CONTENT.ROBOTS.SUCCESS')
              : this.translateService.instant('AIEO.CONTENT.ROBOTS.IMPROVEMENT'),
          score: robotsScore || 0,
        },
        {
          title: 'Metadata',
          content:
            metadataScore > 40
              ? [
                  this.translateService.instant('AIEO.CONTENT.METADATA.BASIC'),
                  this.translateService.instant('AIEO.CONTENT.METADATA.ADD_DESCRIPTIONS'),
                ]
              : [
                  this.translateService.instant('AIEO.CONTENT.METADATA.ADD_TWITTER'),
                  this.translateService.instant('AIEO.CONTENT.METADATA.ADD_META_DESCRIPTIONS'),
                  this.translateService.instant('AIEO.CONTENT.METADATA.ADD_KEYWORDS'),
                ],
          score: metadataScore || 0,
        },
        {
          title: 'Content',
          content:
            contentScore > 30
              ? this.translateService.instant('AIEO.CONTENT.CONTENT.SUCCESS')
              : this.translateService.instant('AIEO.CONTENT.CONTENT.IMPROVEMENT'),
          score: contentScore || 0,
        },
        {
          title: 'Page Load',
          content:
            pageLoadScore > 20
              ? this.translateService.instant('AIEO.CONTENT.PAGE_LOAD.SUCCESS')
              : this.translateService.instant('AIEO.CONTENT.PAGE_LOAD.IMPROVEMENT'),
          score: pageLoadScore || 0,
        },
      ],
    };
  }

  onRunAnotherAudit(): void {
    this.auditData = null;
    this.auditDataService.clearAuditData();
    this.auditForm.reset();
    this.showSuccessMessage = false;
  }

  private clearFormFields(): void {
    this.auditForm.reset();

    Object.keys(this.auditForm.controls).forEach((key) => {
      const control = this.auditForm.get(key);
      if (control) {
        control.markAsPristine();
        control.markAsUntouched();
      }
    });

    this.closeAllDialogs();
  }

  private closeAllDialogs(): void {
    this.dialog.closeAll();
  }
  goToReports(): void {
    this.router.navigate(['./reports'], { relativeTo: this.route });
  }

  markFormGroupTouched() {
    Object.keys(this.auditForm.controls).forEach((key) => {
      const control = this.auditForm.get(key);
      control?.markAsTouched();
    });
  }

  getErrorMessage(controlName: string): string {
    const control = this.auditForm.get(controlName);

    if (control?.hasError('required')) {
      return controlName === 'brandName'
        ? this.translateService.instant('AIEO.INPUT.ERROR.BRAND_NAME_REQUIRED')
        : this.translateService.instant('AIEO.INPUT.ERROR.WEBSITE_REQUIRED');
    }

    if (control?.hasError('minlength')) {
      return this.translateService.instant('AIEO.INPUT.ERROR.BRAND_NAME_MIN_LENGTH');
    }

    if (control?.hasError('invalidUrl')) {
      return this.translateService.instant('AIEO.INPUT.ERROR.INVALID_URL');
    }

    return '';
  }

  private startAutoDismissTimer(): void {
    this.clearAutoDismissTimer();
    this.autoDismissTimer = setTimeout(() => {
      this.dismissSuccessMessage();
    }, 5000);
  }

  private clearAutoDismissTimer(): void {
    if (this.autoDismissTimer) {
      clearTimeout(this.autoDismissTimer);
      this.autoDismissTimer = null;
    }
  }

  dismissSuccessMessage(): void {
    this.showSuccessMessage = false;
    this.clearAutoDismissTimer();
  }
}
