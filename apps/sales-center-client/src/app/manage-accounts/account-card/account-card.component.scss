@use 'design-tokens' as dt;
@use 'breaks' as br;

.business-card__hotness {
  margin-right: dt.$spacing-1;
  margin-left: dt.$spacing-1;
  color: dt.$red;
}

.no-assignee {
  color: dt.$red;
}

mat-card {
  margin-bottom: dt.$spacing-2;
  mat-card-content {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-bottom: dt.$spacing-2;

    @include br.respond-to(mobile) {
      flex-direction: column;
    }
  }

  mat-card-footer {
    background-color: dt.$secondary-background-color;
  }
}

.unread-icon {
  height: dt.$spacing-2;
  width: dt.$spacing-2;
  font-size: dt.$font-preset-6-size;
  margin-right: dt.$spacing-1;
}

.badges {
  margin-top: dt.$spacing-2;
  margin-bottom: dt.$spacing-2;

  glxy-badge {
    margin-right: dt.$spacing-1;
  }
}

.hotness-flames {
  display: inline-flex;
  position: relative;
  top: dt.$spacing-1;
  color: dt.$red;

  mat-icon {
    width: 20px;
    height: 20px;
  }
}

.products {
  position: absolute;
  top: dt.$spacing-2;
  right: dt.$spacing-2;
  display: flex;
  justify-content: flex-end;
}

.stats {
  width: 275px;

  @include br.respond-to(mobile) {
    width: 100%;
  }
}

.stat-item {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: dt.$spacing-1;
}

.footer {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;

  @include br.respond-to(mobile) {
    justify-content: center;
  }
}

.loading-spinner {
  margin-left: dt.$spacing-2;
}

.centralize-icon {
  display: inline-flex;
  place-content: center;
  align-items: center;
}
