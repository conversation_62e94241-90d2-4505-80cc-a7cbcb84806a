<app-page [pageTitle]="'AIEO.REPORTS.PAGE_TITLE' | translate">
  <div class="aieo-nav-tabs">
    <button mat-button class="nav-tab" routerLink="../" routerLinkActive="active">
      <mat-icon>input</mat-icon>
      {{ 'AIEO.NAV.INPUT' | translate }}
    </button>
    <button mat-button class="nav-tab" [class.active]="true" routerLink="./" routerLinkActive="active">
      <mat-icon>assessment</mat-icon>
      {{ 'AIEO.NAV.REPORTS' | translate }}
    </button>
  </div>

  <div class="reports-container">
    <div class="reports-header">
      <h1 class="reports-title">{{ 'AIEO.REPORTS.TITLE' | translate }}</h1>
      <p class="reports-subtitle">{{ 'AIEO.REPORTS.SUBTITLE' | translate }}</p>
    </div>

    <div *ngIf="loading" class="loading-container">
      <mat-card class="loading-card">
        <mat-card-content>
          <div class="loading-content">
            <mat-icon class="loading-icon">hourglass_empty</mat-icon>
            <p>{{ 'AIEO.REPORTS.LOADING' | translate }}</p>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <div *ngIf="!loading && reports.length > 0" class="reports-list">
      <div class="reports-grid">
        <mat-card *ngFor="let report of reports; trackBy: trackByReportId" class="report-card">
          <mat-card-content>
            <div class="report-header-row">
              <div class="report-domain-section">
                <h3 class="report-domain">{{ report.websiteUrl }}</h3>
                <div class="report-brand">{{ report.brandName }}</div>
              </div>
              <div class="report-status-section">
                <glxy-badge [color]="getStatusBadgeColor(report.status)" [size]="'small'" class="status-badge">
                  <mat-icon class="status-icon">{{ getStatusIcon(report.status) }}</mat-icon>
                  {{
                    report.status === 'in_progress'
                      ? ('AIEO.REPORTS.STATUS.IN_PROGRESS' | translate)
                      : report.status === 'completed'
                        ? ('AIEO.REPORTS.STATUS.COMPLETED' | translate)
                        : report.status === 'failed'
                          ? ('AIEO.REPORTS.STATUS.FAILED' | translate)
                          : ('AIEO.REPORTS.STATUS.PENDING' | translate)
                  }}
                </glxy-badge>
              </div>
            </div>

            <div class="report-timestamps">
              <div class="timestamp-item">
                <span class="timestamp-label">{{ 'AIEO.REPORTS.TIMESTAMP.CREATED' | translate }}</span>
                <span class="timestamp-value">{{ report.createdAt | date: 'MMM d, y h:mm a' }}</span>
              </div>
              <div *ngIf="report.completedAt" class="timestamp-item">
                <span class="timestamp-label">{{ 'AIEO.REPORTS.TIMESTAMP.COMPLETED' | translate }}</span>
                <span class="timestamp-value">{{ report.completedAt | date: 'MMM d, y h:mm a' }}</span>
              </div>
            </div>

            <div *ngIf="report.status === 'in_progress' && report.progress !== undefined" class="progress-section">
              <div class="progress-header">
                <span class="progress-label">{{ 'AIEO.REPORTS.PROGRESS.LABEL' | translate }}</span>
                <span class="progress-percentage">{{ report.progress }}%</span>
              </div>
              <mat-progress-bar mode="determinate" [value]="report.progress" color="accent" class="progress-bar">
              </mat-progress-bar>
            </div>

            <div class="report-actions">
              <button
                *ngIf="report.status === 'completed'"
                mat-stroked-button
                color="primary"
                class="action-button"
                (click)="onViewReport(report)"
              >
                <mat-icon>visibility</mat-icon>
                <span>{{ 'AIEO.REPORTS.ACTIONS.VIEW_RESULTS' | translate }}</span>
              </button>
              <button *ngIf="report.status === 'in_progress'" mat-stroked-button disabled class="action-button">
                <mat-icon>hourglass_empty</mat-icon>
                <span>{{ 'AIEO.REPORTS.ACTIONS.PROCESSING' | translate }}</span>
              </button>
              <button *ngIf="report.status === 'failed'" mat-stroked-button color="warn" class="action-button">
                <mat-icon>error</mat-icon>
                <span>{{ 'AIEO.REPORTS.ACTIONS.FAILED' | translate }}</span>
              </button>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>

    <div *ngIf="!loading && reports.length === 0" class="empty-state">
      <mat-card class="empty-card">
        <mat-card-content>
          <div class="empty-content">
            <mat-icon class="empty-icon">assessment</mat-icon>
            <h2>{{ 'AIEO.REPORTS.EMPTY.TITLE' | translate }}</h2>
            <p>{{ 'AIEO.REPORTS.EMPTY.DESCRIPTION' | translate }}</p>
            <button mat-raised-button color="primary" (click)="onRunNewAudit()">
              <mat-icon>add</mat-icon>
              {{ 'AIEO.REPORTS.EMPTY.ACTION' | translate }}
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</app-page>
