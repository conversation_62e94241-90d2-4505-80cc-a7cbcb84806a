import { Inject, Injectable, Optional } from '@angular/core';
import { SalesTeamInterface, Salesperson } from '@galaxy/types';
import { FilterField } from '@vendasta/uikit';
import { Observable, ReplaySubject, SchedulerLike, Subject, combineLatest } from 'rxjs';
import { auditTime, distinctUntilChanged, map, mapTo, scan, shareReplay, take } from 'rxjs/operators';
import { SALESPERSON_ID_TOKEN } from '../../common/providers';
import { ALL_SALESPEOPLE_STARTING_WITH_CURRENT_TOKEN } from '../../data-providers/salespeople';
import { SALES_TEAMS_TOKEN } from '../../sales-teams';
import { LIFECYCLE_OPTIONS_TOKEN, LifecycleStageFilterOption } from '../manage-accounts-filters-lifecycle';
import { TAXONOMY_OPTIONS_TOKEN, Taxonomy } from '../manage-accounts-filters-taxonomy';
import { ACCOUNT_TAGS_TOKEN, AccountGoal, AccountTag, GOALS_TOKEN, SortKeys } from '../manage-accounts.service';

export interface ManageAccountsFilters {
  filters$: Observable<any>;
  filterOptions$: Observable<any>;
}

export interface FilterOptions {
  selectedSalesperson: string;
  salesTeams: SalesTeamInterface[];
  salesPeople: Salesperson[];
  accountTags: AccountTag[];
  goals: AccountGoal[];
  businessCategories: Taxonomy[];
  lifecycleStages: LifecycleStageFilterOption[];
}

export interface FilterSelection {
  filterFieldName: string;
  key: string;
  value: any;
}

/**
 * A service which handles:
 * - Populates the filter options from app-level streams
 * - Produces a single stream of "manage accounts filter" objects with throttling, etc.
 */
@Injectable()
export class ManageAccountsFiltersService implements ManageAccountsFilters {
  readonly filterOptions$: Observable<FilterOptions>;
  readonly filters$: Observable<FilterSelection[]>;
  private readonly filters$$ = new ReplaySubject<FilterField<any>>(1);
  private readonly searchTerm$$ = new ReplaySubject<string>(1);
  readonly searchTerm$ = this.searchTerm$$.asObservable();
  private readonly sortToggles$$ = new Subject<null>();
  readonly sortToggles$ = this.sortToggles$$.asObservable();
  hasPageData$: Observable<boolean[]> = new Observable<boolean[]>();
  private readonly sortField$$ = new ReplaySubject<SortKeys>(1);
  sortField$ = this.sortField$$.asObservable();
  readonly loggedInSalesPerson$: Observable<string>;
  filtersSet = false;

  static determineSortKey(value: string): SortKeys {
    switch (value) {
      case 'hotness':
      case 'created':
      case 'name':
        return value;
      case 'last-customer-activity':
        return 'last-activity-timestamp';
      case 'last-sales-activity':
        return 'last-sales-activity-timestamp';
      case 'last-connected':
        return 'last-connected-timestamp';
      default:
        throw new Error('Unexpected sort value: ' + value);
    }
  }

  constructor(
    @Inject(SALESPERSON_ID_TOKEN) loggedInSalesperson$: Observable<string>,
    @Inject(ALL_SALESPEOPLE_STARTING_WITH_CURRENT_TOKEN) salesPeople$: Observable<Salesperson[]>,
    @Inject(SALES_TEAMS_TOKEN) salesTeams$: Observable<SalesTeamInterface[]>,
    @Inject(ACCOUNT_TAGS_TOKEN) accountTags$: Observable<AccountTag[]>,
    @Inject(GOALS_TOKEN) goals$: Observable<AccountGoal[]>,
    @Inject(TAXONOMY_OPTIONS_TOKEN) businessCategories$: Observable<Taxonomy[]>,
    @Inject(LIFECYCLE_OPTIONS_TOKEN) lifecycleStageOptions$: Observable<LifecycleStageFilterOption[]>,
    @Inject('') @Optional() scheduler?: SchedulerLike,
    @Inject('') @Optional() delayTime?: number,
  ) {
    scheduler = scheduler || undefined;
    delayTime = delayTime || 50;
    this.loggedInSalesPerson$ = loggedInSalesperson$.pipe(take(1));
    this.hasPageData$ = combineLatest([
      salesPeople$.pipe(take(1), mapTo(true)),
      loggedInSalesperson$.pipe(take(1), mapTo(true)),
      salesTeams$.pipe(take(1), mapTo(true)),
      accountTags$.pipe(take(1), mapTo(true)),
    ]);
    this.filterOptions$ = combineLatest([
      loggedInSalesperson$,
      salesPeople$,
      salesTeams$,
      accountTags$,
      goals$,
      businessCategories$,
      lifecycleStageOptions$,
    ]).pipe(
      map(([loggedInSalesperson, salesPeople, salesTeam, accountTags, goals, businessCategories, lifecycleStages]) => {
        return {
          selectedSalesperson: loggedInSalesperson,
          salesPeople: salesPeople,
          salesTeams: salesTeam,
          accountTags: accountTags,
          goals: goals,
          businessCategories: businessCategories,
          lifecycleStages: lifecycleStages,
        } as FilterOptions;
      }),
    );

    this.filters$ = this.getFilters$(delayTime, scheduler);
  }

  private getFilters$(delayTime: number, scheduler: SchedulerLike): Observable<FilterSelection[]> {
    return this.filters$$.asObservable().pipe(
      scan((acc: FilterSelection[], nextFilter): FilterSelection[] => {
        const newField: FilterSelection = {
          filterFieldName: nextFilter.name,
          key: nextFilter.id,
          value: nextFilter.value,
        };
        const existingFieldIndex = acc.findIndex((val) => val.key === newField.key);
        if (existingFieldIndex > -1) {
          acc.splice(existingFieldIndex, 1);
          acc.push(newField);
        } else {
          acc.push(newField);
        }
        return acc;
      }, []),
      auditTime(delayTime, scheduler),
      distinctUntilChanged(
        (prev, next) =>
          prev.map((filter) => ({ key: filter.key, value: filter.value })) ===
          next.map((filter) => ({ key: filter.key, value: filter.value })),
      ),
      shareReplay(1),
    );
  }

  pushEvent($event: FilterField<any>): void {
    this.filters$$.next($event);
    this.filtersSet = true;
  }

  toggleSort(): void {
    this.sortToggles$$.next(null);
  }

  changeSortField(sortField: string): void {
    const sortKey = ManageAccountsFiltersService.determineSortKey(sortField);
    this.sortField$$.next(sortKey);
  }

  setSearchTerm(searchTerm: string): void {
    this.searchTerm$$.next(searchTerm);
  }
}
