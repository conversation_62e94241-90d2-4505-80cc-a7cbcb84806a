@use 'design-tokens' as *;

.audit-results-container {
  width: 100%;
  max-width: 1200px;
  margin-top: $spacing-4;
}

.audit-section {
  margin-bottom: $spacing-3;
}

.audit-section-header {
  padding: $spacing-3 $spacing-3 $spacing-1;
  border-bottom: 1px solid $weak-border-color;

  .section-title {
    @include text-preset-4;
    font-weight: 600;
    color: $primary-text-color;
    margin: 0;
  }
}

.audit-section-content {
  padding: $spacing-3;
}

.audit-card {
  background: $card-background-color;
  border-radius: $default-border-radius * 3;
  box-shadow:
    0 $spacing-1 $spacing-2 -1px rgba(0, 0, 0, 0.1),
    0 $spacing-1 $spacing-1 -1px rgba(0, 0, 0, 0.06);
  border: 1px solid $border-color;
  overflow: hidden;
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow:
      0 $spacing-2 $spacing-4 -3px rgba(0, 0, 0, 0.1),
      0 $spacing-1 $spacing-2 -2px rgba(0, 0, 0, 0.05);
  }
}

.audit-card-header {
  padding: $spacing-3 $spacing-3 $spacing-1;
  border-bottom: 1px solid $weak-border-color;

  .section-title {
    @include text-preset-4;
    font-weight: 600;
    color: $primary-text-color;
    margin: 0;
  }
}

.audit-card-content {
  padding: $spacing-4;
}

.progress-card-layout {
  display: flex;
  gap: $spacing-4;
  align-items: flex-start;
}

.progress-column {
  flex: 0 0 25%;
  display: flex;
  justify-content: center;
}

.content-column {
  flex: 0 0 75%;
}

.circular-gauge-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-3;
  padding: $spacing-2;
  background: linear-gradient(135deg, rgba($primary-color, 0.02) 0%, rgba($blue, 0.02) 100%);
  border-radius: $default-border-radius * 2;
  border: 1px solid rgba($border-color, 0.3);
}

.gauge-wrapper {
  position: relative;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
  cursor: pointer;

  &:hover {
    transform: scale(1.05);
  }
}

.gauge-svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.gauge-background {
  transition: stroke 0.3s ease;
}

.gauge-progress {
  transition: stroke-dashoffset 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 $spacing-1 $spacing-1 rgba(0, 0, 0, 0.1));
  animation: gaugePulse 2s ease-in-out infinite;
}

@keyframes gaugePulse {
  0%,
  100% {
    filter: drop-shadow(0 $spacing-1 $spacing-1 rgba(0, 0, 0, 0.1));
  }
  50% {
    filter: drop-shadow(0 $spacing-1 $spacing-2 rgba(0, 0, 0, 0.15));
  }
}

.gauge-center {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.gauge-score {
  @include text-preset-3--bold;
  color: $primary-text-color;
  line-height: 1;
  margin-bottom: $spacing-1;
}

.gauge-label {
  @include text-preset-6;
  color: $secondary-text-color;
  line-height: 1;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

// Score indicators
.score-indicators {
  display: flex;
  flex-direction: column;
  gap: $spacing-2;
  width: 100%;
  max-width: 140px;
}

.score-indicator {
  display: flex;
  align-items: center;
  gap: $spacing-2;
  padding: $spacing-1;
  border-radius: $default-border-radius;
  transition: all 0.2s ease;
  opacity: 0.6;

  &.active {
    opacity: 1;
    background: rgba($primary-color, 0.05);
  }

  &.current {
    background: rgba($primary-color, 0.1);
    transform: scale(1.05);
  }
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: $tertiary-text-color;
  flex-shrink: 0;
  transition: all 0.2s ease;

  .score-indicator.active & {
    background: $primary-color;
    box-shadow: 0 0 0 $spacing-1 rgba($primary-color, 0.2);
  }

  .score-indicator.current & {
    background: $primary-color;
    box-shadow: 0 0 0 3px rgba($primary-color, 0.3);
    transform: scale(1.2);
  }
}

.indicator-label {
  @include text-preset-6;
  color: $secondary-text-color;
  line-height: 1;
  flex: 1;
  text-align: left;
}

.audit-summary {
  flex: 1;
}

.audit-description {
  @include text-preset-4;
  line-height: 1.6;
  color: $secondary-text-color;
  margin: 0;
}

@media (max-width: 768px) {
  .progress-card-layout {
    flex-direction: column;
    gap: $spacing-3;
  }

  .progress-column {
    flex: none;
    order: -1;
  }

  .content-column {
    flex: none;
  }

  .gauge-wrapper {
    width: 100px;
    height: 100px;
  }

  .gauge-score {
    @include text-preset-4;
  }

  .gauge-label {
    @include text-preset-6;
  }

  .score-indicators {
    max-width: 120px;
  }
}

@media (max-width: 480px) {
  .audit-card-content {
    padding: $spacing-3;
  }

  .gauge-wrapper {
    width: 90px;
    height: 90px;
  }

  .gauge-score {
    @include text-preset-5;
  }

  .gauge-label {
    @include text-preset-6;
  }

  .score-indicators {
    max-width: 100px;
  }
}

.progress-indicator {
  display: flex;
  align-items: flex-start;
  gap: $spacing-3;
  padding: $spacing-3;
  background: $card-background-color;
  border: 1px solid $border-color;
  border-radius: $default-border-radius;
  transition: all 0.2s ease;

  &:hover {
    border-color: $primary-color;
    box-shadow: 0 $spacing-1 $spacing-1 -1px rgba($primary-color, 0.1);
  }

  &.overall-progress {
    background: linear-gradient(135deg, rgba($primary-color, 0.05) 0%, rgba($blue, 0.05) 100%);
    border-color: $primary-color;

    .progress-percentage {
      background: linear-gradient(135deg, $primary-color 0%, $blue 100%);
      color: $white;
      font-weight: 700;
    }
  }

  @media (max-width: 640px) {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-2;
  }
}

.progress-percentage {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  height: 48px;
  padding: 0 $spacing-3;
  background: $weak-border-color;
  color: $secondary-text-color;
  border-radius: $default-border-radius;
  @include text-preset-4;
  font-weight: 600;
  white-space: nowrap;
  flex-shrink: 0;

  @media (max-width: 768px) {
    min-width: 70px;
    height: 40px;
    @include text-preset-5;
  }

  @media (max-width: 480px) {
    min-width: 60px;
    height: 36px;
    @include text-preset-6;
  }
}

.progress-description {
  flex: 1;
  min-width: 0; // Allows text to wrap properly
}

.audit-text {
  @include text-preset-4;
  line-height: 1.6;
  color: $secondary-text-color;
  margin: 0;
}

.audit-list {
  list-style: none;
  padding: 0;
  margin: 0;

  li {
    position: relative;
    padding-left: $spacing-3;
    margin-bottom: $spacing-2;
    @include text-preset-4;
    line-height: 1.6;
    color: $secondary-text-color;

    &:last-child {
      margin-bottom: 0;
    }

    &::before {
      content: '•';
      position: absolute;
      left: 0;
      color: $tertiary-text-color;
      font-weight: bold;
    }
  }
}

.audit-footer {
  display: flex;
  justify-content: center;
  margin-top: $spacing-5;
  padding-top: $spacing-4;
  border-top: 1px solid $border-color;

  .run-another-button {
    min-width: 160px;
    height: 48px;
    @include text-preset-4;
    font-weight: 500;
    border-radius: $default-border-radius * 2;
    text-transform: none;
    box-shadow: 0 $spacing-1 $spacing-2 -1px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 $spacing-2 $spacing-2 -1px rgba(0, 0, 0, 0.15);
    }
  }
}

@media (max-width: 768px) {
  .audit-card-content,
  .audit-section-content {
    padding: $spacing-3;
  }

  .audit-card-header,
  .audit-section-header {
    padding: $spacing-3 $spacing-3 $spacing-1;
  }

  .progress-indicator {
    padding: $spacing-2;
  }
}

@media (max-width: 480px) {
  .audit-card-content,
  .audit-section-content {
    padding: $spacing-2;
  }

  .audit-card-header,
  .audit-section-header {
    padding: $spacing-2 $spacing-2 $spacing-1;
  }

  .progress-indicator {
    padding: $spacing-2;
  }
}
