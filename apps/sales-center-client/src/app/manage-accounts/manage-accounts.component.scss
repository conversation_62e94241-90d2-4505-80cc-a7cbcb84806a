@use 'design-tokens' as *;

@media screen and (max-width: $media--tablet-minimum) {
  :host ::ng-deep {
    .toolbar .table-controls-row {
      flex-flow: row wrap;
      align-items: center;
    }

    .toolbar .filter-button {
      order: 1;
      margin-bottom: $spacing-4;
    }

    .toolbar .filter-button.selected {
      margin: 6px 6px 26px;
    }

    .toolbar .table-controls-row .search-and-text {
      order: 2;
    }

    .toolbar .table-controls-row div:nth-child(4) {
      order: 3;
      flex: 1 auto;
      text-align: center;

      mat-form-field {
        padding-right: $spacing-3;
      }
    }
  }

  div#header-div {
    mat-form-field {
      padding-right: 0;
      margin: 0 $spacing-2 0 $spacing-2;
    }
    mat-icon#sort-arrow {
      margin-right: $spacing-2;
    }
  }
}

.accounts-container {
  margin: 0 auto;
  max-width: 1200px;
}

uikit-empty-state {
  margin: 64px;
}

select {
  color: $blue;
}

.total-results {
  margin: $spacing-2 0;
}

.card-sorter {
  display: flex;
  flex-flow: row nowrap;

  mat-icon {
    margin: auto 0 auto $spacing-2;
    cursor: pointer;
    user-select: none;
  }
}

.accounts-footer-container {
  text-align: center;
}

div#header-div {
  display: flex;
  align-items: center;
  flex-flow: row wrap;

  button#more-options {
    background: white;
    margin: $spacing-2;
    color: $blue;
    flex-grow: 1;

    span.early-access {
      margin: -2px 0 1px $spacing-2;
      background: $blue;
      display: inline-block;
      line-height: 1;
      border-radius: $default-border-radius;
      padding: 3px;
      color: white;
      font-size: $font-preset-5-size;
    }
  }

  mat-icon#sort-arrow {
    cursor: pointer;
  }

  mat-form-field {
    margin: $spacing-2 $spacing-2 0;
    flex-grow: 1;
  }
}

.empty-states {
  padding: 16px;
}
