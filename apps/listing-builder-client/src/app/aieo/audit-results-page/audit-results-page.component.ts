import { Component, OnInit, OnD<PERSON>roy, ChangeDetectionStrategy, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { Subject, takeUntil, Observable, take } from 'rxjs';
import { Mat<PERSON>ard, MatCardContent } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { SharedModule } from '../../shared/shared.module';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { AuditResultsComponent } from '../audit-results/audit-results.component';
import { AuditDataService } from '../audit-data.service';
import { ReportsService } from '../reports/reports.service';
import { AuditData } from '../audit-results/audit.interface';
import { AuditReport } from '../reports/reports.interface';
import { AGIDTOKEN } from '../../app.module';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-audit-results-page',
  templateUrl: './audit-results-page.component.html',
  styleUrls: ['./audit-results-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.Default,
  imports: [
    CommonModule,
    MatCard,
    MatCardContent,
    MatButtonModule,
    SharedModule,
    GalaxyFormFieldModule,
    AuditResultsComponent,
    TranslateModule,
  ],
  standalone: true,
})
export class AuditResultsPageComponent implements OnInit, OnDestroy {
  auditData: AuditData | null = null;
  currentReport: AuditReport | null = null;
  private destroy$ = new Subject<void>();

  constructor(
    private auditDataService: AuditDataService,
    private reportsService: ReportsService,
    private router: Router,
    private route: ActivatedRoute,
    @Inject(AGIDTOKEN) private accountGroupId$: Observable<string>,
  ) {}

  ngOnInit(): void {
    this.currentReport = this.reportsService.getCurrentReport();

    if (this.currentReport && this.currentReport.auditData) {
      this.auditData = this.currentReport.auditData;
      this.auditDataService.setAuditData(this.auditData);
    } else {
      this.auditDataService.auditData$.pipe(takeUntil(this.destroy$)).subscribe((data) => {
        this.auditData = data;
        if (!data) {
          this.accountGroupId$.pipe(take(1)).subscribe((accountGroupId) => {
            this.router.navigate([`/edit/account/${accountGroupId}/app/aieo`]);
          });
        }
      });

      const existingData = this.auditDataService.getAuditData();
      if (existingData) {
        this.auditData = existingData;
      } else {
        this.accountGroupId$.pipe(take(1)).subscribe((accountGroupId) => {
          this.router.navigate([`/edit/account/${accountGroupId}/app/aieo`]);
        });
      }
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onRunAnotherAudit(): void {
    this.auditDataService.clearAuditData();
    this.reportsService.clearCurrentReport();
    this.accountGroupId$.pipe(take(1)).subscribe((accountGroupId) => {
      this.router.navigate([`/edit/account/${accountGroupId}/app/aieo`]);
    });
  }
}
