import { Inject, Injectable } from '@angular/core';
import { AccountGroupService, ProjectionFilter } from '@galaxy/account-group';
import { AccountGroupSdkService, AggregationSupportedFields, MarketingInfo } from '@vendasta/account-group';
import { SwitchingWorkState, WorkStates } from '@vendasta/rx-utils/work-state';
import {
  Archived,
  BusinessApi,
  BusinessSearchResponse,
  Businesses,
  Campaign,
  LifecycleStage,
  Snapshot,
} from '@vendasta/sales';
import { BusinessInterface } from '@vendasta/sales/lib/_internal';
import { BehaviorSubject, Observable, ReplaySubject, Subject, combineLatest, from, merge, of } from 'rxjs';
import { catchError, filter, map, shareReplay, switchMap, tap } from 'rxjs/operators';
import { Md5 } from 'ts-md5/dist/md5';
import { ApiService } from '../common';
import { BusinessPrioritiesService } from '../common/business-priorities.service';
import { USER_INFO_TOKEN } from '../core/feature-flag.service';
import { LoggedInUserInfo } from '../logged-in-user-info';
import { SalesStatusFilterId, SalespersonActionId, SimpleManageAccountsFilter } from './manage-accounts-filters';
import { Taxonomy } from './manage-accounts-filters-taxonomy';
import { TeamsOfSalespeopleService } from './teams-of-salespeople.service';

export interface Business {
  partnerId: string;
  marketId: string;
  name: string;
  accountGroupId: string;
  hotness: number;
  city: string;
  zip: string;
  address: string;
  country: string;
  state: string;
  phoneNumber: string;
  salesPersonId: string;
  lastSalesActivityDate: Date;
  lastCustomerActivityDate: Date;
  salesStatus: string;
  salesStatusTag: string;
  isRead: boolean;
  salesPersonAction: string;
  salesPersonActionFormatted: string;
  snapshotReportUrl: string;
  latestSnapshotExpiry: Date;
  snapshotOrEmailCampaignStatus: boolean;
  activityType: string;
  lastConnectedDate: Date;
  marketingInfo: MarketingInfo;
}

export interface ActivityEvent {
  activityId: SalesStatusFilterId;
  value: boolean;
}

export interface AccountTag {
  name: string;
  value: string;
}

export interface AccountGoal {
  name: string;
  value: string;
  description: string;
}

export type SortKeys =
  | 'last-connected-timestamp'
  | 'last-sales-activity-timestamp'
  | 'last-activity-timestamp'
  | 'name'
  | 'created'
  | 'hotness';

export interface LoadBusinessEvent {
  cursor?: string;
  pageSize?: number;
  salesPeopleIds?: string[];
  salesTeamId?: string;
  activity?: ActivityEvent;
  salespersonActivity?: SalespersonActionId;
  createdAfterDate?: Date;
  createdBeforeDate?: Date;
  archiveStatus?: Archived;
  businessCategories?: Taxonomy[];
  campaignStatus?: Campaign;
  snapshotStatus?: Snapshot;
  accountTags?: SimpleManageAccountsFilter[];
  searchTerm?: string;
  sortValue?: SortKeys;
  sortAscending?: boolean;
  goals?: SimpleManageAccountsFilter;
  trainingPriorities?: SimpleManageAccountsFilter;
  lifecycleStage?: LifecycleStage;
}

export interface LoadMoreBusinessEvent {
  loadMoreValues: LoadMoreValues;
  currentBusiness?: Business[];
}

interface LoadBusinessInputs {
  groupId: string;
  event: LoadMoreBusinessEvent;
  currentSalespersonId: string;
  partnerId: string;
  marketId: string;
  salesStatuses: string[];
  salesAction: string;
  fromDate: Date;
  toDate: Date;
  archived: Archived;
  accountTags: string[];
  businessCategories: string[];
  onCampaign: Campaign;
  snapshotSent: Snapshot;
  assignees: string[];
  query: string;
  goal: string;
  trainingPriorities: string;
  sortKey: string;
  sortAscending: boolean;
  lifecycleStage: LifecycleStage;
}

export interface BusinessLoader {
  salesPerson$: Observable<LoggedInUserInfo>;
  totalResults$: Observable<number>;
  businessState$: WorkStates<BusinessSearchResponse>;
  moreBusinessState$: WorkStates<Business[]>;
  loadMoreValues$: Observable<LoadMoreValues>;
  businessResult$: Observable<Business[]>;
  searchingWithEmptyTeam$: Observable<boolean>;

  initLoadBusinesses(loadBusinessesEvent: LoadBusinessEvent): void;

  loadMoreBusinesses(loadMoreBusinessEvent: LoadMoreBusinessEvent): void;

  getAccountTags(): Observable<AccountTag[]>;

  getGoals(): Observable<AccountGoal[]>;
}

export interface LoadMoreValues {
  nextCursor: string;
  loadMore: boolean;
}

export const PageSize = 25;

export const ACCOUNT_TAGS_TOKEN = 'ACCOUNT_TAGS_TOKEN';
export const GOALS_TOKEN = 'GOALS_TOKEN';

@Injectable()
export class ManageAccountsService implements BusinessLoader {
  constructor(
    @Inject(USER_INFO_TOKEN) private readonly userInfo$: Observable<LoggedInUserInfo>,
    private readonly api: ApiService,
    @Inject(Businesses) private readonly businesses: BusinessApi,
    private readonly teams: TeamsOfSalespeopleService,
    private readonly businessPrioritiesService: BusinessPrioritiesService,
    private readonly accountGroupService: AccountGroupService,
    private readonly accountGroupSdkService: AccountGroupSdkService,
  ) {
    this.salesPerson$ = userInfo$;
    this.loadMoreValues$ = this.loadMoreValues$$.asObservable();
    combineLatest([userInfo$, this.loadBusinessEvents$$]).subscribe(([user, loadBusinessEvent]) => {
      const inputs = this.getInputs(loadBusinessEvent, user);
      this.lastEvent = inputs;
      this.businessState$.startWork(inputs);
    });
    this.loadMoreBusinessEvents$$.subscribe((e) => {
      this.moreBusinessState$.startWork(e);
    });

    this.businessResult$ = merge(
      this.businessState$.workResults$.pipe(switchMap((r) => this.makeUiBusinessFromResponse(r))),
      this.moreBusinessState$.workResults$,
    );
    this.totalResults$ = this.businessState$.workResults$.pipe(
      tap((r) => {
        const values = <LoadMoreValues>{
          nextCursor: r.pagingMetadata.nextCursor,
          loadMore: r.pagingMetadata.hasMore,
        };
        this.loadMoreValues$$.next(values);
      }),
      map((r) => (r.pagingMetadata.totalResults ? r.pagingMetadata.totalResults : 0)),
    );
  }

  salesPerson$: Observable<LoggedInUserInfo>;
  readonly loadMoreValues$: Observable<LoadMoreValues>;
  readonly businessState$ = new SwitchingWorkState<LoadBusinessInputs, BusinessSearchResponse>((e) =>
    this.fetchBusinesses(e),
  );

  private readonly searchingWithEmptyTeam$$ = new ReplaySubject<boolean>(1);
  readonly searchingWithEmptyTeam$ = this.searchingWithEmptyTeam$$.asObservable();

  readonly moreBusinessState$ = new SwitchingWorkState<LoadMoreBusinessEvent, Business[]>((e: LoadMoreBusinessEvent) =>
    this.fetchBusinesses(this.lastEvent, e.loadMoreValues).pipe(
      tap((r) => {
        const values = <LoadMoreValues>{
          nextCursor: r.pagingMetadata.nextCursor,
          loadMore: r.pagingMetadata.hasMore,
        };
        this.loadMoreValues$$.next(values);
      }),
      switchMap((r) => this.makeUiBusinessFromResponse(r)),
      map((businesses) => [...e.currentBusiness, ...businesses]),
    ),
  );
  private readonly loadBusinessEvents$$ = new Subject<LoadBusinessEvent>();
  private readonly loadMoreBusinessEvents$$ = new Subject<LoadMoreBusinessEvent>();
  private readonly loadMoreValues$$ = new BehaviorSubject<LoadMoreValues>({
    nextCursor: '',
    loadMore: false,
  });
  readonly businessResult$: Observable<Business[]>;
  readonly totalResults$: Observable<number>;

  private readonly filteredSalesStatuses: SalesStatusFilterId[] = [];
  private filteredSalesPeople: string[] = [];
  private filteredSalesTeam: string = null;
  private filteredSalespersonActivity: SalespersonActionId = SalespersonActionId.ANY;
  private filteredCreatedBeforeDate: Date = null;
  private filteredCreatedAfterDate: Date = null;
  private filteredArchivedOption: Archived = Archived.ARCHIVED_NO;
  private filteredBusinessCategories: Taxonomy[] = [];
  private filteredCampaignOption: Campaign = Campaign.CAMPAIGN_ANY;
  private filteredSnapshotStatus: Snapshot = Snapshot.SNAPSHOT_ANY;
  private filteredAccountTags: SimpleManageAccountsFilter[] = [];
  private filteredGoals: SimpleManageAccountsFilter;
  private filteredTrainingPriorities: SimpleManageAccountsFilter;
  private filteredLifecycleStage: LifecycleStage;
  private searchTerm: string;
  private sortKey: SortKeys = 'hotness';
  private sortAscending = false;

  private lastEvent: LoadBusinessInputs;

  private static assigneesToSearch(assignees: string[], teamAssignees: string[]): string[] {
    if (!Array.isArray(assignees) || !assignees.length) {
      assignees = [];
    }
    if (!Array.isArray(teamAssignees) || !teamAssignees.length) {
      teamAssignees = [];
    }
    if (assignees.length === 0 && teamAssignees.length === 0) {
      return null;
    } else {
      if (assignees.length > 0 && teamAssignees.length > 0) {
        return assignees.concat(teamAssignees);
      }
      if (assignees.length > 0) {
        return assignees;
      }
      return teamAssignees;
    }
  }

  private addLifecycleStageToBiz(businesses: Business[]): Observable<Business[]> {
    const projectionFilter = new ProjectionFilter({ marketingInfo: true });

    if (businesses.length === 0) {
      return of([]);
    }

    return this.accountGroupService
      .getMulti(
        businesses.map((b) => b?.accountGroupId),
        projectionFilter,
      )
      .pipe(
        filter((account) => !!account),
        map((getMultiAccountGroups) => {
          businesses.forEach((b) => {
            const marketingInfo = getMultiAccountGroups.find(
              (a) => a?.accountGroupId === b?.accountGroupId,
            )?.marketingInfo;
            b.marketingInfo = marketingInfo ?? new MarketingInfo();
          });
          return businesses;
        }),
        catchError(() => {
          console.error('Failed to retrieve lifecycle stage');
          return of([]);
        }),
      );
  }

  private getInputs(event: LoadBusinessEvent, user: LoggedInUserInfo): LoadBusinessInputs {
    return <LoadBusinessInputs>{
      event: event,
      partnerId: user.partnerId,
      marketId: user.marketId,
      sortKey: this.sortKey,
      groupId: this.filteredSalesTeam,
      sortAscending: this.sortAscending,
      salesStatuses: this.filteredSalesStatuses.length !== 0 ? this.filteredSalesStatuses : null,
      salesAction: this.filteredSalespersonActivity !== '' ? this.filteredSalespersonActivity : null,
      currentSalespersonId: user.salespersonId,
      assignees: this.filteredSalesPeople,
      fromDate: this.filteredCreatedAfterDate,
      toDate: this.filteredCreatedBeforeDate,
      archived: this.filteredArchivedOption,
      accountTags: this.filteredAccountTags.length !== 0 ? this.filteredAccountTags.map((es) => es.name) : null,
      businessCategories:
        this.filteredBusinessCategories.length !== 0 ? this.filteredBusinessCategories.map((ed) => ed.id) : null,
      onCampaign: this.filteredCampaignOption,
      snapshotSent: this.filteredSnapshotStatus,
      query: this.searchTerm !== '' && this.searchTerm !== undefined ? this.searchTerm : null,
      goal: this.filteredGoals ? this.filteredGoals.value : null,
      trainingPriorities: this.filteredTrainingPriorities ? this.filteredTrainingPriorities.value : null,
      lifecycleStage: this.filteredLifecycleStage ? this.filteredLifecycleStage : LifecycleStage.LIFECYCLE_STAGE_UNSET,
    };
  }

  private fetchBusinesses(
    inputs: LoadBusinessInputs,
    loadMoreValues?: LoadMoreValues,
  ): Observable<BusinessSearchResponse> {
    return combineLatest([this.teams.getMembers(inputs.groupId), this.userInfo$]).pipe(
      switchMap(([members, user]) => {
        let assignees: string[] = null;
        if (!user.hasAccessToAllAccountsInMarket && this.filteredSalesPeople.length === 0) {
          assignees = [user.salespersonId];
        } else {
          const searchingWithEmptyTeam = !!inputs.groupId && (members || []).length === 0;
          this.searchingWithEmptyTeam$$.next(searchingWithEmptyTeam);
          assignees = ManageAccountsService.assigneesToSearch(
            inputs.assignees ? inputs.assignees : [],
            members ? members : [],
          );
        }
        const searchOptions = {
          searchTerm: inputs.query,
          salesStatuses: inputs.salesStatuses,
          salesAction: inputs.salesAction,
          fromDate: inputs.fromDate,
          toDate: inputs.toDate,
          archived: inputs.archived,
          accountTags: inputs.accountTags,
          businessCategories: inputs.businessCategories,
          onCampaign: inputs.onCampaign,
          snapshotSent: inputs.snapshotSent,
          assignees: assignees,
          cursor: loadMoreValues ? loadMoreValues.nextCursor : null,
          pageSize: PageSize,
          goal: inputs.goal,
          trainingPriorities: inputs.trainingPriorities,
          lifecycleStage: inputs.lifecycleStage,
        };

        return this.businesses.search(
          inputs.partnerId,
          inputs.marketId,
          inputs.sortKey,
          inputs.sortAscending,
          searchOptions,
        );
      }),
      map((res: BusinessSearchResponse) => {
        return res;
      }),
    );
  }

  private makeUiBusinessFromResponse(response: BusinessSearchResponse): Observable<Business[]> {
    const businesses = response.businesses.map((snakeBiz) => {
      return convertResponseToViewBusiness(snakeBiz);
    });

    return this.addLifecycleStageToBiz(businesses);
  }

  getAccountTags(): Observable<AccountTag[]> {
    return this.salesPerson$.pipe(
      switchMap((salesperson) => {
        return this.accountGroupSdkService
          .listOptionsForField({
            partnerId: salesperson.partnerId,
            marketId: salesperson.marketId || 'default',
            field: AggregationSupportedFields.AGGREGATION_TAGS,
          })
          .pipe(map((tags) => tags.map((tag) => ({ name: tag, value: tag }))));
      }),
      shareReplay(1),
    );
  }

  getGoals(): Observable<AccountGoal[]> {
    return this.salesPerson$.pipe(
      switchMap((pm) => {
        return from(this.businessPrioritiesService.listGoalsForPartner({ partnerId: pm.partnerId })).pipe(
          map((resp) => {
            const optionsMap: AccountGoal[] = [];
            if (resp.goals) {
              resp.goals.forEach((k) => {
                optionsMap.push(<AccountGoal>{ name: k.title, value: k.id, description: k.description });
              });
            }
            return optionsMap;
          }),
        );
      }),
    );
  }

  initLoadBusinesses(loadBusinessesEvent: LoadBusinessEvent): void {
    if (loadBusinessesEvent.salesTeamId !== undefined) {
      this.filteredSalesTeam = loadBusinessesEvent.salesTeamId !== null ? loadBusinessesEvent.salesTeamId : '';
    }
    if (loadBusinessesEvent.salesPeopleIds !== undefined) {
      this.filteredSalesPeople = loadBusinessesEvent.salesPeopleIds;
    }
    if (loadBusinessesEvent.activity !== undefined) {
      if (loadBusinessesEvent.activity.value === true) {
        if (this.filteredSalesStatuses.indexOf(loadBusinessesEvent.activity.activityId) === -1) {
          this.filteredSalesStatuses.push(loadBusinessesEvent.activity.activityId);
        }
      } else {
        this.filteredSalesStatuses.splice(
          this.filteredSalesStatuses.indexOf(loadBusinessesEvent.activity.activityId),
          1,
        );
      }
    }
    if (loadBusinessesEvent.salespersonActivity !== undefined) {
      this.filteredSalespersonActivity = loadBusinessesEvent.salespersonActivity;
    }
    if (loadBusinessesEvent.createdBeforeDate !== undefined) {
      this.filteredCreatedBeforeDate = loadBusinessesEvent.createdBeforeDate;
    }
    if (loadBusinessesEvent.createdAfterDate !== undefined) {
      this.filteredCreatedAfterDate = loadBusinessesEvent.createdAfterDate;
    }
    if (loadBusinessesEvent.archiveStatus !== undefined) {
      this.filteredArchivedOption = loadBusinessesEvent.archiveStatus;
    }
    if (loadBusinessesEvent.businessCategories !== undefined) {
      this.filteredBusinessCategories = loadBusinessesEvent.businessCategories;
    }
    if (loadBusinessesEvent.accountTags !== undefined) {
      this.filteredAccountTags = loadBusinessesEvent.accountTags;
    }
    if (loadBusinessesEvent.campaignStatus !== undefined) {
      this.filteredCampaignOption = loadBusinessesEvent.campaignStatus;
    }
    if (loadBusinessesEvent.snapshotStatus !== undefined) {
      this.filteredSnapshotStatus = loadBusinessesEvent.snapshotStatus;
    }
    if (loadBusinessesEvent.sortValue !== undefined) {
      this.sortKey = loadBusinessesEvent.sortValue;
    }
    if (loadBusinessesEvent.sortAscending !== undefined) {
      this.sortAscending = loadBusinessesEvent.sortAscending;
    }
    if (loadBusinessesEvent.searchTerm !== undefined) {
      this.searchTerm = loadBusinessesEvent.searchTerm;
    }
    if (loadBusinessesEvent.goals !== undefined) {
      this.filteredGoals = loadBusinessesEvent.goals;
    }
    if (loadBusinessesEvent.trainingPriorities !== undefined) {
      this.filteredTrainingPriorities = loadBusinessesEvent.trainingPriorities;
    }
    if (loadBusinessesEvent.lifecycleStage !== undefined) {
      this.filteredLifecycleStage = loadBusinessesEvent.lifecycleStage;
    }

    this.loadBusinessEvents$$.next(loadBusinessesEvent);
    this.loadMoreValues$$.next({
      nextCursor: '',
      loadMore: false,
    });
  }

  loadMoreBusinesses(loadMoreBusinessEvent: LoadMoreBusinessEvent): void {
    if (
      loadMoreBusinessEvent.loadMoreValues.nextCursor !== undefined &&
      loadMoreBusinessEvent.loadMoreValues.nextCursor !== ''
    ) {
      this.loadMoreBusinessEvents$$.next(loadMoreBusinessEvent);
    } else {
      this.loadMoreValues$$.next({
        nextCursor: '',
        loadMore: false,
      });
    }
  }
}

function convertResponseToViewBusiness(biz: BusinessInterface): Business {
  return {
    partnerId: biz.partnerId,
    marketId: biz.marketId,
    name: biz.name,
    accountGroupId: biz.accountGroupId,
    hotness: biz.hotness ? biz.hotness : 0,
    city: biz.city,
    zip: biz.zip,
    address: biz.address,
    state: biz.state,
    country: biz.country,
    phoneNumber: biz.phoneNumber,
    salesPersonId: biz.salespersonId,
    lastSalesActivityDate: biz.lastSalesActivityDate ? new Date(biz.lastSalesActivityDate) : undefined,
    lastCustomerActivityDate: biz.lastCustomerActivityDate ? new Date(biz.lastCustomerActivityDate) : undefined,
    salesStatus: formatSalesRecordStatus(biz.salesStatus),
    salesStatusTag: biz.salesStatus,
    isRead: biz.isRead ? biz.isRead : false,
    salesPersonAction: biz.salesPersonAction,
    salesPersonActionFormatted: formatSalespersonAction(biz.salesPersonAction),
    snapshotReportUrl: snapshotReportURL(biz.accountGroupId),
    latestSnapshotExpiry: biz.latestSnapshotExpiry ? new Date(biz.latestSnapshotExpiry) : undefined,
    snapshotOrEmailCampaignStatus: biz.snapshotOrCampaignEmailStatus ? biz.snapshotOrCampaignEmailStatus : false,
    activityType: biz.activityType,
    lastConnectedDate: biz.lastConnectedDate ? new Date(biz.lastConnectedDate) : undefined,
    marketingInfo: undefined,
  };
}

function snapshotReportURL(accountGroupId: string): string {
  const hashedAccountGroupId = Md5.hashStr(accountGroupId) as string;
  return `report/${hashedAccountGroupId}/edit/?agid=${accountGroupId}`;
}

enum SalespersonActions {
  'replied-by-email' = 'Replied by email',
  'email-sent' = 'Email sent',
  'email-received' = 'Email received',
  'inbound-call' = 'Inbound call',
  'outbound-call' = 'Outbound call',
  'presentation-booked' = 'Presentation booked',
  'presentation-done' = 'Presentation done',
  'meeting' = 'Meeting',
  'opportunity-created' = 'Opportunity created',
  'opportunity-closed-won' = 'Opportunity closed (won)',
  'opportunity-closed-lost' = 'Opportunity closed (lost)',
  'opportunity-updated' = 'Opportunity updated',
  'archived-business' = 'Archived',
  'unarchived-business' = 'Unarchived',
  'opportunity-reopened' = 'Opportunity reopened',
  'snapshot-created' = 'Snapshot created',
  'account-created' = 'Account created',
  'other' = 'Other',
  'submitted-for-customer-approval' = 'Submitted for customer approval',
}

function formatSalespersonAction(rawAction: string): string {
  for (const item of Object.keys(SalespersonActions)) {
    if (item === rawAction) {
      return SalespersonActions[item];
    }
  }
}

enum SalesRecordStatuses {
  'account-created' = 'Account created',
  'ready-to-sell' = 'Ready to sell',
  'in-progress' = 'In progress',
  'follow-up-needed' = 'Follow up needed',
  'closed-won' = 'Closed (won)',
  'closed-lost' = 'Closed (lost)',
  'snapshot-created' = 'Snapshot created',
}

function formatSalesRecordStatus(rawAction: string): string {
  for (const item of Object.keys(SalesRecordStatuses)) {
    if (item === rawAction) {
      return SalesRecordStatuses[item];
    }
  }
}
