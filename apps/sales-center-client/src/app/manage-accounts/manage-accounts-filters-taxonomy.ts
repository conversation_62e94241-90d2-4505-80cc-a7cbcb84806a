/* tslint:disable max-file-line-count */
/**
 * Taxonomy options taken from FrontendCentral/dist/components/taxonomy-selector/options
 * TODO possibly move this to business categories sdk once it's ready
 */
import { Observable, of } from 'rxjs';

export interface Taxonomy {
  id: string;
  name: string;
}

export const TAXONOMY_OPTIONS = <Taxonomy[]>[
  {
    id: 'active',
    name: 'Active Life',
  },
  {
    id: 'active:amateursportsteams',
    name: 'Amateur Sports Teams',
  },
  {
    id: 'active:amusementparks',
    name: 'Amusement Parks',
  },
  {
    id: 'active:aquariums',
    name: 'Aquariums',
  },
  {
    id: 'active:archery',
    name: '<PERSON><PERSON>',
  },
  {
    id: 'active:badminton',
    name: '<PERSON><PERSON><PERSON>',
  },
  {
    id: 'active:basketballcourts',
    name: 'Basketball Courts',
  },
  {
    id: 'active:beaches',
    name: 'Beaches',
  },
  {
    id: 'active:bikerentals',
    name: 'Bike Rentals',
  },
  {
    id: 'active:boating',
    name: 'Boating',
  },
  {
    id: 'active:bowling',
    name: 'Bowling',
  },
  {
    id: 'active:climbing',
    name: 'Climbing',
  },
  {
    id: 'active:discgolf',
    name: 'Disc Golf',
  },
  {
    id: 'active:diving',
    name: 'Diving',
  },
  {
    id: 'active:diving:freediving',
    name: 'Free Diving',
  },
  {
    id: 'active:diving:scuba',
    name: 'Scuba Diving',
  },
  {
    id: 'active:fishing',
    name: 'Fishing',
  },
  {
    id: 'active:fitness',
    name: 'Fitness & Instruction',
  },
  {
    id: 'active:fitness:barreclasses',
    name: 'Barre Classes',
  },
  {
    id: 'active:fitness:bootcamps',
    name: 'Boot Camps',
  },
  {
    id: 'active:fitness:boxing',
    name: 'Boxing',
  },
  {
    id: 'active:fitness:dancestudio',
    name: 'Dance Studios',
  },
  {
    id: 'active:fitness:gyms',
    name: 'Gyms',
  },
  {
    id: 'active:fitness:martialarts',
    name: 'Martial Arts',
  },
  {
    id: 'active:fitness:pilates',
    name: 'Pilates',
  },
  {
    id: 'active:fitness:swimminglessons',
    name: 'Swimming Lessons/Schools',
  },
  {
    id: 'active:fitness:taichi',
    name: 'Tai Chi',
  },
  {
    id: 'active:fitness:healthtrainers',
    name: 'Trainers',
  },
  {
    id: 'active:fitness:yoga',
    name: 'Yoga',
  },
  {
    id: 'active:gokarts',
    name: 'Go Karts',
  },
  {
    id: 'active:golf',
    name: 'Golf',
  },
  {
    id: 'active:gun_ranges',
    name: 'Gun/Rifle Ranges',
  },
  {
    id: 'active:gymnastics',
    name: 'Gymnastics',
  },
  {
    id: 'active:hanggliding',
    name: 'Hang Gliding',
  },
  {
    id: 'active:hiking',
    name: 'Hiking',
  },
  {
    id: 'active:horseracing',
    name: 'Horse Racing',
  },
  {
    id: 'active:horsebackriding',
    name: 'Horseback Riding',
  },
  {
    id: 'active:hot_air_balloons',
    name: 'Hot Air Balloons',
  },
  {
    id: 'active:kiteboarding',
    name: 'Kiteboarding',
  },
  {
    id: 'active:lakes',
    name: 'Lakes',
  },
  {
    id: 'active:lasertag',
    name: 'Laser Tag',
  },
  {
    id: 'active:leisure_centers',
    name: 'Leisure Centers',
  },
  {
    id: 'active:mini_golf',
    name: 'Mini Golf',
  },
  {
    id: 'active:mountainbiking',
    name: 'Mountain Biking',
  },
  {
    id: 'active:paddleboarding',
    name: 'Paddleboarding',
  },
  {
    id: 'active:paintball',
    name: 'Paintball',
  },
  {
    id: 'active:parks',
    name: 'Parks',
  },
  {
    id: 'active:parks:dog_parks',
    name: 'Dog Parks',
  },
  {
    id: 'active:parks:skate_parks',
    name: 'Skate Parks',
  },
  {
    id: 'active:playgrounds',
    name: 'Playgrounds',
  },
  {
    id: 'active:rafting',
    name: 'Rafting/Kayaking',
  },
  {
    id: 'active:recreation',
    name: 'Recreation Centers',
  },
  {
    id: 'active:rock_climbing',
    name: 'Rock Climbing',
  },
  {
    id: 'active:skatingrinks',
    name: 'Skating Rinks',
  },
  {
    id: 'active:skydiving',
    name: 'Skydiving',
  },
  {
    id: 'active:football',
    name: 'Soccer',
  },
  {
    id: 'active:spinclasses',
    name: 'Spin Classes',
  },
  {
    id: 'active:sports_clubs',
    name: 'Sports Clubs',
  },
  {
    id: 'active:squash',
    name: 'Squash',
  },
  {
    id: 'active:summer_camps',
    name: 'Summer Camps',
  },
  {
    id: 'active:surfing',
    name: 'Surfing',
  },
  {
    id: 'active:swimmingpools',
    name: 'Swimming Pools',
  },
  {
    id: 'active:tennis',
    name: 'Tennis',
  },
  {
    id: 'active:trampoline',
    name: 'Trampoline Parks',
  },
  {
    id: 'active:tubing',
    name: 'Tubing',
  },
  {
    id: 'active:zoos',
    name: 'Zoos',
  },
  {
    id: 'arts',
    name: 'Arts & Entertainment',
  },
  {
    id: 'arts:arcades',
    name: 'Arcades',
  },
  {
    id: 'arts:galleries',
    name: 'Art Galleries',
  },
  {
    id: 'arts:gardens',
    name: 'Botanical Gardens',
  },
  {
    id: 'arts:casinos',
    name: 'Casinos',
  },
  {
    id: 'arts:movietheaters',
    name: 'Cinema',
  },
  {
    id: 'arts:culturalcenter',
    name: 'Cultural Center',
  },
  {
    id: 'arts:festivals',
    name: 'Festivals',
  },
  {
    id: 'arts:jazzandblues',
    name: 'Jazz & Blues',
  },
  {
    id: 'arts:museums',
    name: 'Museums',
  },
  {
    id: 'arts:musicvenues',
    name: 'Music Venues',
  },
  {
    id: 'arts:opera',
    name: 'Opera & Ballet',
  },
  {
    id: 'arts:theater',
    name: 'Performing Arts',
  },
  {
    id: 'arts:sportsteams',
    name: 'Professional Sports Teams',
  },
  {
    id: 'arts:psychic_astrology',
    name: 'Psychics & Astrologers',
  },
  {
    id: 'arts:racetracks',
    name: 'Race Tracks',
  },
  {
    id: 'arts:social_clubs',
    name: 'Social Clubs',
  },
  {
    id: 'arts:stadiumsarenas',
    name: 'Stadiums & Arenas',
  },
  {
    id: 'arts:ticketsales',
    name: 'Ticket Sales',
  },
  {
    id: 'arts:wineries',
    name: 'Wineries',
  },
  {
    id: 'auto',
    name: 'Automotive',
  },
  {
    id: 'auto:auto_detailing',
    name: 'Auto Detailing',
  },
  {
    id: 'auto:autoglass',
    name: 'Auto Glass Services',
  },
  {
    id: 'auto:autoloanproviders',
    name: 'Auto Loan Providers',
  },
  {
    id: 'auto:autopartssupplies',
    name: 'Auto Parts & Supplies',
  },
  {
    id: 'auto:autorepair',
    name: 'Auto Repair',
  },
  {
    id: 'auto:boatdealers',
    name: 'Boat Dealers',
  },
  {
    id: 'auto:bodyshops',
    name: 'Body Shops',
  },
  {
    id: 'auto:car_dealers',
    name: 'Car Dealers',
  },
  {
    id: 'auto:stereo_installation',
    name: 'Car Stereo Installation',
  },
  {
    id: 'auto:carwash',
    name: 'Car Wash',
  },
  {
    id: 'auto:servicestations',
    name: 'Gas & Service Stations',
  },
  {
    id: 'auto:motorcycledealers',
    name: 'Motorcycle Dealers',
  },
  {
    id: 'auto:motorcyclerepair',
    name: 'Motorcycle Repair',
  },
  {
    id: 'auto:oilchange',
    name: 'Oil Change Stations',
  },
  {
    id: 'auto:parking',
    name: 'Parking',
  },
  {
    id: 'auto:rv_dealers',
    name: 'RV Dealers',
  },
  {
    id: 'auto:smog_check_stations',
    name: 'Smog Check Stations',
  },
  {
    id: 'auto:tires',
    name: 'Tires',
  },
  {
    id: 'auto:towing',
    name: 'Towing',
  },
  {
    id: 'auto:truck_rental',
    name: 'Truck Rental',
  },
  {
    id: 'auto:windshieldinstallrepair',
    name: 'Windshield Installation & Repair',
  },
  {
    id: 'beautysvc',
    name: 'Beauty & Spas',
  },
  {
    id: 'beautysvc:barbers',
    name: 'Barbers',
  },
  {
    id: 'beautysvc:cosmetics',
    name: 'Cosmetics & Beauty Supply',
  },
  {
    id: 'beautysvc:spas',
    name: 'Day Spas',
  },
  {
    id: 'beautysvc:eyelashservice',
    name: 'Eyelash Service',
  },
  {
    id: 'beautysvc:hair_extensions',
    name: 'Hair Extensions',
  },
  {
    id: 'beautysvc:hairremoval',
    name: 'Hair Removal',
  },
  {
    id: 'beautysvc:hairremoval:laser_hair_removal',
    name: 'Laser Hair Removal',
  },
  {
    id: 'beautysvc:hair',
    name: 'Hair Salons',
  },
  {
    id: 'beautysvc:hair:blowoutservices',
    name: 'Blow Dry/Out Services',
  },
  {
    id: 'beautysvc:hair:hair_extensions',
    name: 'Hair Extensions',
  },
  {
    id: 'beautysvc:hair:hairstylists',
    name: 'Hair Stylists',
  },
  {
    id: 'beautysvc:hair:menshair',
    name: "Men's Hair Salons",
  },
  {
    id: 'beautysvc:makeupartists',
    name: 'Makeup Artists',
  },
  {
    id: 'beautysvc:massage',
    name: 'Massage',
  },
  {
    id: 'beautysvc:medicalspa',
    name: 'Medical Spas',
  },
  {
    id: 'beautysvc:othersalons',
    name: 'Nail Salons',
  },
  {
    id: 'beautysvc:permanentmakeup',
    name: 'Permanent Makeup',
  },
  {
    id: 'beautysvc:piercing',
    name: 'Piercing',
  },
  {
    id: 'beautysvc:rolfing',
    name: 'Rolfing',
  },
  {
    id: 'beautysvc:skincare',
    name: 'Skin Care',
  },
  {
    id: 'beautysvc:tanning',
    name: 'Tanning',
  },
  {
    id: 'beautysvc:tanning:spraytanning',
    name: 'Spray Tanning',
  },
  {
    id: 'beautysvc:tanning:tanningbeds',
    name: 'Tanning Beds',
  },
  {
    id: 'beautysvc:tattoo',
    name: 'Tattoo',
  },
  {
    id: 'education',
    name: 'Education',
  },
  {
    id: 'education:adultedu',
    name: 'Adult Education',
  },
  {
    id: 'education:collegecounseling',
    name: 'College Counseling',
  },
  {
    id: 'education:collegeuniv',
    name: 'Colleges & Universities',
  },
  {
    id: 'education:educationservices',
    name: 'Educational Services',
  },
  {
    id: 'education:elementaryschools',
    name: 'Elementary Schools',
  },
  {
    id: 'education:highschools',
    name: 'Middle Schools & High Schools',
  },
  {
    id: 'education:preschools',
    name: 'Preschools',
  },
  {
    id: 'education:privatetutors',
    name: 'Private Tutors',
  },
  {
    id: 'education:religiousschools',
    name: 'Religious Schools',
  },
  {
    id: 'education:specialed',
    name: 'Special Education',
  },
  {
    id: 'education:specialtyschools',
    name: 'Specialty Schools',
  },
  {
    id: 'education:specialtyschools:artschools',
    name: 'Art Schools',
  },
  {
    id: 'education:specialtyschools:cprclasses',
    name: 'CPR Classes',
  },
  {
    id: 'education:specialtyschools:cookingschools',
    name: 'Cooking Schools',
  },
  {
    id: 'education:specialtyschools:cosmetology_schools',
    name: 'Cosmetology Schools',
  },
  {
    id: 'education:specialtyschools:dance_schools',
    name: 'Dance Schools',
  },
  {
    id: 'education:specialtyschools:driving_schools',
    name: 'Driving Schools',
  },
  {
    id: 'education:specialtyschools:firstaidclasses',
    name: 'First Aid Classes',
  },
  {
    id: 'education:specialtyschools:flightinstruction',
    name: 'Flight Instruction',
  },
  {
    id: 'education:specialtyschools:language_schools',
    name: 'Language Schools',
  },
  {
    id: 'education:specialtyschools:massage_schools',
    name: 'Massage Schools',
  },
  {
    id: 'education:specialtyschools:swimminglessons',
    name: 'Swimming Lessons/Schools',
  },
  {
    id: 'education:specialtyschools:vocation',
    name: 'Vocational & Technical School',
  },
  {
    id: 'education:testprep',
    name: 'Test Preparation',
  },
  {
    id: 'education:tutoring',
    name: 'Tutoring Centers',
  },
  {
    id: 'eventservices',
    name: 'Event Planning & Services',
  },
  {
    id: 'eventservices:bartenders',
    name: 'Bartenders',
  },
  {
    id: 'eventservices:boatcharters',
    name: 'Boat Charters',
  },
  {
    id: 'eventservices:stationery',
    name: 'Cards & Stationery',
  },
  {
    id: 'eventservices:catering',
    name: 'Caterers',
  },
  {
    id: 'eventservices:clowns',
    name: 'Clowns',
  },
  {
    id: 'eventservices:djs',
    name: 'DJs',
  },
  {
    id: 'eventservices:hotels',
    name: 'Hotels',
  },
  {
    id: 'eventservices:magicians',
    name: 'Magicians',
  },
  {
    id: 'eventservices:musicians',
    name: 'Musicians',
  },
  {
    id: 'eventservices:officiants',
    name: 'Officiants',
  },
  {
    id: 'eventservices:eventplanning',
    name: 'Party & Event Planning',
  },
  {
    id: 'eventservices:partybusrentals',
    name: 'Party Bus Rentals',
  },
  {
    id: 'eventservices:partyequipmentrentals',
    name: 'Party Equipment Rentals',
  },
  {
    id: 'eventservices:partysupplies',
    name: 'Party Supplies',
  },
  {
    id: 'eventservices:personalchefs',
    name: 'Personal Chefs',
  },
  {
    id: 'eventservices:photographers',
    name: 'Photographers',
  },
  {
    id: 'eventservices:photographers:eventphotography',
    name: 'Event Photography',
  },
  {
    id: 'eventservices:photographers:sessionphotography',
    name: 'Session Photography',
  },
  {
    id: 'eventservices:venues',
    name: 'Venues & Event Spaces',
  },
  {
    id: 'eventservices:videographers',
    name: 'Videographers',
  },
  {
    id: 'eventservices:wedding_planning',
    name: 'Wedding Planning',
  },
  {
    id: 'financialservices',
    name: 'Financial Services',
  },
  {
    id: 'financialservices:banks',
    name: 'Banks & Credit Unions',
  },
  {
    id: 'financialservices:paydayloans',
    name: 'Check Cashing/Pay-day Loans',
  },
  {
    id: 'financialservices:financialadvising',
    name: 'Financial Advising',
  },
  {
    id: 'financialservices:insurance',
    name: 'Insurance',
  },
  {
    id: 'financialservices:investing',
    name: 'Investing',
  },
  {
    id: 'financialservices:taxservices',
    name: 'Tax Services',
  },
  {
    id: 'food',
    name: 'Food',
  },
  {
    id: 'food:bagels',
    name: 'Bagels',
  },
  {
    id: 'food:bakeries',
    name: 'Bakeries',
  },
  {
    id: 'food:beer_and_wine',
    name: 'Beer, Wine & Spirits',
  },
  {
    id: 'food:breweries',
    name: 'Breweries',
  },
  {
    id: 'food:bubbletea',
    name: 'Bubble Tea',
  },
  {
    id: 'food:butcher',
    name: 'Butcher',
  },
  {
    id: 'food:csa',
    name: 'CSA',
  },
  {
    id: 'food:coffee',
    name: 'Coffee & Tea',
  },
  {
    id: 'food:convenience',
    name: 'Convenience Stores',
  },
  {
    id: 'food:desserts',
    name: 'Desserts',
  },
  {
    id: 'food:diyfood',
    name: 'Do-It-Yourself Food',
  },
  {
    id: 'food:donuts',
    name: 'Donuts',
  },
  {
    id: 'food:farmersmarket',
    name: 'Farmers Market',
  },
  {
    id: 'food:fooddeliveryservices',
    name: 'Food Delivery Services',
  },
  {
    id: 'food:foodtrucks',
    name: 'Food Trucks',
  },
  {
    id: 'food:gelato',
    name: 'Gelato',
  },
  {
    id: 'food:grocery',
    name: 'Grocery',
  },
  {
    id: 'food:icecream',
    name: 'Ice Cream & Frozen Yogurt',
  },
  {
    id: 'food:internetcafe',
    name: 'Internet Cafes',
  },
  {
    id: 'food:juicebars',
    name: 'Juice Bars & Smoothies',
  },
  {
    id: 'food:pretzels',
    name: 'Pretzels',
  },
  {
    id: 'food:shavedice',
    name: 'Shaved Ice',
  },
  {
    id: 'food:gourmet',
    name: 'Specialty Food',
  },
  {
    id: 'food:gourmet:candy',
    name: 'Candy Stores',
  },
  {
    id: 'food:gourmet:cheese',
    name: 'Cheese Shops',
  },
  {
    id: 'food:gourmet:chocolate',
    name: 'Chocolatiers & Shops',
  },
  {
    id: 'food:gourmet:ethnicmarkets',
    name: 'Ethnic Food',
  },
  {
    id: 'food:gourmet:markets',
    name: 'Fruits & Veggies',
  },
  {
    id: 'food:gourmet:healthmarkets',
    name: 'Health Markets',
  },
  {
    id: 'food:gourmet:herbsandspices',
    name: 'Herbs & Spices',
  },
  {
    id: 'food:gourmet:meats',
    name: 'Meat Shops',
  },
  {
    id: 'food:gourmet:seafoodmarkets',
    name: 'Seafood Markets',
  },
  {
    id: 'food:streetvendors',
    name: 'Street Vendors',
  },
  {
    id: 'food:tea',
    name: 'Tea Rooms',
  },
  {
    id: 'food:wineries',
    name: 'Wineries',
  },
  {
    id: 'health',
    name: 'Health & Medical',
  },
  {
    id: 'health:acupuncture',
    name: 'Acupuncture',
  },
  {
    id: 'health:cannabis_clinics',
    name: 'Cannabis Clinics',
  },
  {
    id: 'health:chiropractors',
    name: 'Chiropractors',
  },
  {
    id: 'health:c_and_mh',
    name: 'Counseling & Mental Health',
  },
  {
    id: 'health:dentists',
    name: 'Dentists',
  },
  {
    id: 'health:dentists:cosmeticdentists',
    name: 'Cosmetic Dentists',
  },
  {
    id: 'health:dentists:endodontists',
    name: 'Endodontists',
  },
  {
    id: 'health:dentists:generaldentistry',
    name: 'General Dentistry',
  },
  {
    id: 'health:dentists:oralsurgeons',
    name: 'Oral Surgeons',
  },
  {
    id: 'health:dentists:orthodontists',
    name: 'Orthodontists',
  },
  {
    id: 'health:dentists:pediatric_dentists',
    name: 'Pediatric Dentists',
  },
  {
    id: 'health:dentists:periodontists',
    name: 'Periodontists',
  },
  {
    id: 'health:diagnosticservices',
    name: 'Diagnostic Services',
  },
  {
    id: 'health:diagnosticservices:diagnosticimaging',
    name: 'Diagnostic Imaging',
  },
  {
    id: 'health:diagnosticservices:laboratorytesting',
    name: 'Laboratory Testing',
  },
  {
    id: 'health:physicians',
    name: 'Doctors',
  },
  {
    id: 'health:physicians:allergist',
    name: 'Allergists',
  },
  {
    id: 'health:physicians:anesthesiologists',
    name: 'Anesthesiologists',
  },
  {
    id: 'health:physicians:audiologist',
    name: 'Audiologist',
  },
  {
    id: 'health:physicians:cardiology',
    name: 'Cardiologists',
  },
  {
    id: 'health:physicians:cosmeticsurgeons',
    name: 'Cosmetic Surgeons',
  },
  {
    id: 'health:physicians:dermatology',
    name: 'Dermatologists',
  },
  {
    id: 'health:physicians:earnosethroat',
    name: 'Ear Nose & Throat',
  },
  {
    id: 'health:physicians:familydr',
    name: 'Family Practice',
  },
  {
    id: 'health:physicians:fertility',
    name: 'Fertility',
  },
  {
    id: 'health:physicians:gastroenterologist',
    name: 'Gastroenterologist',
  },
  {
    id: 'health:physicians:gerontologist',
    name: 'Gerontologists',
  },
  {
    id: 'health:physicians:internalmed',
    name: 'Internal Medicine',
  },
  {
    id: 'health:physicians:naturopathic',
    name: 'Naturopathic/Holistic',
  },
  {
    id: 'health:physicians:neurologist',
    name: 'Neurologist',
  },
  {
    id: 'health:physicians:obgyn',
    name: 'Obstetricians & Gynecologists',
  },
  {
    id: 'health:physicians:oncologist',
    name: 'Oncologist',
  },
  {
    id: 'health:physicians:opthamalogists',
    name: 'Ophthalmologists',
  },
  {
    id: 'health:physicians:orthopedists',
    name: 'Orthopedists',
  },
  {
    id: 'health:physicians:osteopathicphysicians',
    name: 'Osteopathic Physicians',
  },
  {
    id: 'health:physicians:pediatricians',
    name: 'Pediatricians',
  },
  {
    id: 'health:physicians:podiatrists',
    name: 'Podiatrists',
  },
  {
    id: 'health:physicians:proctologist',
    name: 'Proctologists',
  },
  {
    id: 'health:physicians:psychiatrists',
    name: 'Psychiatrists',
  },
  {
    id: 'health:physicians:pulmonologist',
    name: 'Pulmonologist',
  },
  {
    id: 'health:physicians:sportsmed',
    name: 'Sports Medicine',
  },
  {
    id: 'health:physicians:tattooremoval',
    name: 'Tattoo Removal',
  },
  {
    id: 'health:physicians:urologists',
    name: 'Urologists',
  },
  {
    id: 'health:hearingaidproviders',
    name: 'Hearing Aid Providers',
  },
  {
    id: 'health:homehealthcare',
    name: 'Home Health Care',
  },
  {
    id: 'health:hospice',
    name: 'Hospice',
  },
  {
    id: 'health:hospitals',
    name: 'Hospitals',
  },
  {
    id: 'health:lactationservices',
    name: 'Lactation Services',
  },
  {
    id: 'health:laserlasikeyes',
    name: 'Laser Eye Surgery/Lasik',
  },
  {
    id: 'health:massage_therapy',
    name: 'Massage Therapy',
  },
  {
    id: 'health:medcenters',
    name: 'Medical Centers',
  },
  {
    id: 'health:medicalspa',
    name: 'Medical Spas',
  },
  {
    id: 'health:medicaltransportation',
    name: 'Medical Transportation',
  },
  {
    id: 'health:midwives',
    name: 'Midwives',
  },
  {
    id: 'health:nutritionists',
    name: 'Nutritionists',
  },
  {
    id: 'health:occupationaltherapy',
    name: 'Occupational Therapy',
  },
  {
    id: 'health:optometrists',
    name: 'Optometrists',
  },
  {
    id: 'health:physicaltherapy',
    name: 'Physical Therapy',
  },
  {
    id: 'health:reflexology',
    name: 'Reflexology',
  },
  {
    id: 'health:rehabilitation_center',
    name: 'Rehabilitation Center',
  },
  {
    id: 'health:retirement_homes',
    name: 'Retirement Homes',
  },
  {
    id: 'health:speech_therapists',
    name: 'Speech Therapists',
  },
  {
    id: 'health:tcm',
    name: 'Traditional Chinese Medicine',
  },
  {
    id: 'health:urgent_care',
    name: 'Urgent Care',
  },
  {
    id: 'health:weightlosscenters',
    name: 'Weight Loss Centers',
  },
  {
    id: 'homeservices',
    name: 'Home Services',
  },
  {
    id: 'homeservices:buildingsupplies',
    name: 'Building Supplies',
  },
  {
    id: 'homeservices:carpetinstallation',
    name: 'Carpet Installation',
  },
  {
    id: 'homeservices:carpeting',
    name: 'Carpeting',
  },
  {
    id: 'homeservices:contractors',
    name: 'Contractors',
  },
  {
    id: 'homeservices:damagerestoration',
    name: 'Damage Restoration',
  },
  {
    id: 'homeservices:electricians',
    name: 'Electricians',
  },
  {
    id: 'homeservices:flooring',
    name: 'Flooring',
  },
  {
    id: 'homeservices:garage_door_services',
    name: 'Garage Door Services',
  },
  {
    id: 'homeservices:gardeners',
    name: 'Gardeners',
  },
  {
    id: 'homeservices:handyman',
    name: 'Handyman',
  },
  {
    id: 'homeservices:hvac',
    name: 'Heating & Air Conditioning/HVAC',
  },
  {
    id: 'homeservices:homecleaning',
    name: 'Home Cleaning',
  },
  {
    id: 'homeservices:home_inspectors',
    name: 'Home Inspectors',
  },
  {
    id: 'homeservices:home_organization',
    name: 'Home Organization',
  },
  {
    id: 'homeservices:hometheatreinstallation',
    name: 'Home Theatre Installation',
  },
  {
    id: 'homeservices:homewindowtinting',
    name: 'Home Window Tinting',
  },
  {
    id: 'homeservices:interiordesign',
    name: 'Interior Design',
  },
  {
    id: 'homeservices:isps',
    name: 'Internet Service Providers',
  },
  {
    id: 'homeservices:irrigation',
    name: 'Irrigation',
  },
  {
    id: 'homeservices:locksmiths',
    name: 'Keys & Locksmiths',
  },
  {
    id: 'homeservices:landscapearchitects',
    name: 'Landscape Architects',
  },
  {
    id: 'homeservices:landscaping',
    name: 'Landscaping',
  },
  {
    id: 'homeservices:lighting',
    name: 'Lighting Fixtures & Equipment',
  },
  {
    id: 'homeservices:masonry_concrete',
    name: 'Masonry/Concrete',
  },
  {
    id: 'homeservices:movers',
    name: 'Movers',
  },
  {
    id: 'homeservices:painters',
    name: 'Painters',
  },
  {
    id: 'homeservices:plumbing',
    name: 'Plumbing',
  },
  {
    id: 'homeservices:poolcleaners',
    name: 'Pool Cleaners',
  },
  {
    id: 'homeservices:realestate',
    name: 'Real Estate',
  },
  {
    id: 'homeservices:realestate:apartments',
    name: 'Apartments',
  },
  {
    id: 'homeservices:realestate:commercialrealestate',
    name: 'Commercial Real Estate',
  },
  {
    id: 'homeservices:realestate:homestaging',
    name: 'Home Staging',
  },
  {
    id: 'homeservices:realestate:mortgagebrokers',
    name: 'Mortgage Brokers',
  },
  {
    id: 'homeservices:realestate:prefabricated',
    name: 'Prefabricated',
  },
  {
    id: 'homeservices:realestate:propertymgmt',
    name: 'Property Management',
  },
  {
    id: 'homeservices:realestate:realestateagents',
    name: 'Real Estate Agents',
  },
  {
    id: 'homeservices:realestate:realestatesvcs',
    name: 'Real Estate Services',
  },
  {
    id: 'homeservices:realestate:sharedofficespaces',
    name: 'Shared Office Spaces',
  },
  {
    id: 'homeservices:realestate:university_housing',
    name: 'University Housing',
  },
  {
    id: 'homeservices:roofing',
    name: 'Roofing',
  },
  {
    id: 'homeservices:securitysystems',
    name: 'Security Systems',
  },
  {
    id: 'homeservices:blinds',
    name: 'Shades & Blinds',
  },
  {
    id: 'homeservices:solarinstallation',
    name: 'Solar Installation',
  },
  {
    id: 'homeservices:televisionserviceproviders',
    name: 'Television Service Providers',
  },
  {
    id: 'homeservices:treeservices',
    name: 'Tree Services',
  },
  {
    id: 'homeservices:utilities',
    name: 'Utilities',
  },
  {
    id: 'homeservices:windowwashing',
    name: 'Window Washing',
  },
  {
    id: 'homeservices:windowsinstallation',
    name: 'Windows Installation',
  },
  {
    id: 'hotelstravel',
    name: 'Hotels & Travel',
  },
  {
    id: 'hotelstravel:airports',
    name: 'Airports',
  },
  {
    id: 'hotelstravel:bedbreakfast',
    name: 'Bed & Breakfast',
  },
  {
    id: 'hotelstravel:campgrounds',
    name: 'Campgrounds',
  },
  {
    id: 'hotelstravel:carrental',
    name: 'Car Rental',
  },
  {
    id: 'hotelstravel:guesthouses',
    name: 'Guest Houses',
  },
  {
    id: 'hotelstravel:hostels',
    name: 'Hostels',
  },
  {
    id: 'hotelstravel:hotels',
    name: 'Hotels',
  },
  {
    id: 'hotelstravel:motorcycle_rental',
    name: 'Motorcycle Rental',
  },
  {
    id: 'hotelstravel:rvparks',
    name: 'RV Parks',
  },
  {
    id: 'hotelstravel:rvrental',
    name: 'RV Rental',
  },
  {
    id: 'hotelstravel:resorts',
    name: 'Resorts',
  },
  {
    id: 'hotelstravel:skiresorts',
    name: 'Ski Resorts',
  },
  {
    id: 'hotelstravel:tours',
    name: 'Tours',
  },
  {
    id: 'hotelstravel:trainstations',
    name: 'Train Stations',
  },
  {
    id: 'hotelstravel:transport',
    name: 'Transportation',
  },
  {
    id: 'hotelstravel:transport:airlines',
    name: 'Airlines',
  },
  {
    id: 'hotelstravel:transport:airport_shuttles',
    name: 'Airport Shuttles',
  },
  {
    id: 'hotelstravel:transport:limos',
    name: 'Limos',
  },
  {
    id: 'hotelstravel:transport:publictransport',
    name: 'Public Transportation',
  },
  {
    id: 'hotelstravel:transport:taxis',
    name: 'Taxis',
  },
  {
    id: 'hotelstravel:travelservices',
    name: 'Travel Services',
  },
  {
    id: 'hotelstravel:vacationrentalagents',
    name: 'Vacation Rental Agents',
  },
  {
    id: 'hotelstravel:vacation_rentals',
    name: 'Vacation Rentals',
  },
  {
    id: 'industgoodsmanu',
    name: 'Industrial Goods and Manufacturing',
  },
  {
    id: 'ict',
    name: 'Information and Communication Technology',
  },
  {
    id: 'ict:telecommunicationserviceprovider',
    name: 'Telecommunications Service Provider',
  },
  {
    id: 'ict:managedserviceprovider',
    name: 'Managed Service Provider',
  },
  {
    id: 'ict:valueaddedreseller',
    name: 'Value Added Reseller',
  },
  {
    id: 'ict:cablecompany',
    name: 'Cable Company',
  },
  {
    id: 'ict:internetserviceprovider',
    name: 'Internet Service Provider',
  },
  {
    id: 'localservices',
    name: 'Local Services',
  },
  {
    id: 'localservices:homeappliancerepair',
    name: 'Appliances & Repair',
  },
  {
    id: 'localservices:bailbondsmen',
    name: 'Bail Bondsmen',
  },
  {
    id: 'localservices:bike_repair_maintenance',
    name: 'Bike Repair/Maintenance',
  },
  {
    id: 'localservices:carpet_cleaning',
    name: 'Carpet Cleaning',
  },
  {
    id: 'localservices:childcare',
    name: 'Child Care & Day Care',
  },
  {
    id: 'localservices:nonprofit',
    name: 'Community Service/Non-Profit',
  },
  {
    id: 'localservices:couriers',
    name: 'Couriers & Delivery Services',
  },
  {
    id: 'localservices:drycleaninglaundry',
    name: 'Dry Cleaning & Laundry',
  },
  {
    id: 'localservices:electronicsrepair',
    name: 'Electronics Repair',
  },
  {
    id: 'localservices:funeralservices',
    name: 'Funeral Services & Cemeteries',
  },
  {
    id: 'localservices:reupholstery',
    name: 'Furniture Reupholstery',
  },
  {
    id: 'localservices:itservices',
    name: 'IT Services & Computer Repair',
  },
  {
    id: 'localservices:itservices:datarecovery',
    name: 'Data Recovery',
  },
  {
    id: 'localservices:itservices:mobilephonerepair',
    name: 'Mobile Phone Repair',
  },
  {
    id: 'localservices:jewelryrepair',
    name: 'Jewelry Repair',
  },
  {
    id: 'localservices:junkremovalandhauling',
    name: 'Junk Removal & Hauling',
  },
  {
    id: 'localservices:nannys',
    name: 'Nanny Services',
  },
  {
    id: 'localservices:notaries',
    name: 'Notaries',
  },
  {
    id: 'localservices:pest_control',
    name: 'Pest Control',
  },
  {
    id: 'localservices:copyshops',
    name: 'Printing Services',
  },
  {
    id: 'localservices:recording_studios',
    name: 'Recording & Rehearsal Studios',
  },
  {
    id: 'localservices:recyclingcenter',
    name: 'Recycling Center',
  },
  {
    id: 'localservices:screenprinting',
    name: 'Screen Printing',
  },
  {
    id: 'localservices:screen_printing_tshirt_printing',
    name: 'Screen Printing/T-Shirt Printing',
  },
  {
    id: 'localservices:selfstorage',
    name: 'Self Storage',
  },
  {
    id: 'localservices:sewingalterations',
    name: 'Sewing & Alterations',
  },
  {
    id: 'localservices:shipping_centers',
    name: 'Shipping Centers',
  },
  {
    id: 'localservices:shoerepair',
    name: 'Shoe Repair',
  },
  {
    id: 'localservices:snowremoval',
    name: 'Snow Removal',
  },
  {
    id: 'localservices:watch_repair',
    name: 'Watch Repair',
  },
  {
    id: 'massmedia',
    name: 'Mass Media',
  },
  {
    id: 'massmedia:printmedia',
    name: 'Print Media',
  },
  {
    id: 'massmedia:radiostations',
    name: 'Radio Stations',
  },
  {
    id: 'massmedia:televisionstations',
    name: 'Television Stations',
  },
  {
    id: 'mineag',
    name: 'Mining & Agriculture',
  },
  {
    id: 'mineag:ag',
    name: 'Agriculture',
  },
  {
    id: 'mineag:ag:horticulture',
    name: 'Horticulture',
  },
  {
    id: 'mineag:ag:livestock',
    name: 'Livestock',
  },
  {
    id: 'mineag:mining',
    name: 'Mining',
  },
  {
    id: 'nightlife',
    name: 'Nightlife',
  },
  {
    id: 'nightlife:adultentertainment',
    name: 'Adult Entertainment',
  },
  {
    id: 'nightlife:bars',
    name: 'Bars',
  },
  {
    id: 'nightlife:bars:champagne_bars',
    name: 'Champagne Bars',
  },
  {
    id: 'nightlife:bars:cocktailbars',
    name: 'Cocktail Bars',
  },
  {
    id: 'nightlife:bars:divebars',
    name: 'Dive Bars',
  },
  {
    id: 'nightlife:bars:gaybars',
    name: 'Gay Bars',
  },
  {
    id: 'nightlife:bars:hookah_bars',
    name: 'Hookah Bars',
  },
  {
    id: 'nightlife:bars:lounges',
    name: 'Lounges',
  },
  {
    id: 'nightlife:bars:pubs',
    name: 'Pubs',
  },
  {
    id: 'nightlife:bars:sportsbars',
    name: 'Sports Bars',
  },
  {
    id: 'nightlife:bars:wine_bars',
    name: 'Wine Bars',
  },
  {
    id: 'nightlife:comedyclubs',
    name: 'Comedy Clubs',
  },
  {
    id: 'nightlife:countrydancehalls',
    name: 'Country Dance Halls',
  },
  {
    id: 'nightlife:danceclubs',
    name: 'Dance Clubs',
  },
  {
    id: 'nightlife:jazzandblues',
    name: 'Jazz & Blues',
  },
  {
    id: 'nightlife:karaoke',
    name: 'Karaoke',
  },
  {
    id: 'nightlife:musicvenues',
    name: 'Music Venues',
  },
  {
    id: 'nightlife:pianobars',
    name: 'Piano Bars',
  },
  {
    id: 'nightlife:poolhalls',
    name: 'Pool Halls',
  },
  {
    id: 'other',
    name: 'Other',
  },
  {
    id: 'pets',
    name: 'Pets',
  },
  {
    id: 'pets:animalshelters',
    name: 'Animal Shelters',
  },
  {
    id: 'pets:horse_boarding',
    name: 'Horse Boarding',
  },
  {
    id: 'pets:petservices',
    name: 'Pet Services',
  },
  {
    id: 'pets:petservices:dogwalkers',
    name: 'Dog Walkers',
  },
  {
    id: 'pets:petservices:pet_sitting',
    name: 'Pet Boarding/Pet Sitting',
  },
  {
    id: 'pets:petservices:groomer',
    name: 'Pet Groomers',
  },
  {
    id: 'pets:petservices:pet_training',
    name: 'Pet Training',
  },
  {
    id: 'pets:petstore',
    name: 'Pet Stores',
  },
  {
    id: 'pets:vet',
    name: 'Veterinarians',
  },
  {
    id: 'professional',
    name: 'Professional Services',
  },
  {
    id: 'professional:accountants',
    name: 'Accountants',
  },
  {
    id: 'professional:advertising',
    name: 'Advertising',
  },
  {
    id: 'professional:architects',
    name: 'Architects',
  },
  {
    id: 'professional:boatrepair',
    name: 'Boat Repair',
  },
  {
    id: 'professional:careercounseling',
    name: 'Career Counseling',
  },
  {
    id: 'professional:editorialservices',
    name: 'Editorial Services',
  },
  {
    id: 'professional:employmentagencies',
    name: 'Employment Agencies',
  },
  {
    id: 'professional:graphicdesign',
    name: 'Graphic Design',
  },
  {
    id: 'professional:isps',
    name: 'Internet Service Providers',
  },
  {
    id: 'professional:lawyers',
    name: 'Lawyers',
  },
  {
    id: 'professional:lawyers:bankruptcy',
    name: 'Bankruptcy Law',
  },
  {
    id: 'professional:lawyers:businesslawyers',
    name: 'Business Law',
  },
  {
    id: 'professional:lawyers:criminaldefense',
    name: 'Criminal Defense Law',
  },
  {
    id: 'professional:lawyers:duilawyers',
    name: 'DUI Law',
  },
  {
    id: 'professional:lawyers:divorce',
    name: 'Divorce & Family Law',
  },
  {
    id: 'professional:lawyers:employmentlawyers',
    name: 'Employment Law',
  },
  {
    id: 'professional:lawyers:estateplanning',
    name: 'Estate Planning Law',
  },
  {
    id: 'professional:lawyers:general_litigation',
    name: 'General Litigation',
  },
  {
    id: 'professional:lawyers:immigrationlawyers',
    name: 'Immigration Law',
  },
  {
    id: 'professional:lawyers:personal_injury',
    name: 'Personal Injury Law',
  },
  {
    id: 'professional:lawyers:realestatelawyers',
    name: 'Real Estate Law',
  },
  {
    id: 'professional:legalservices',
    name: 'Legal Services',
  },
  {
    id: 'professional:lifecoach',
    name: 'Life Coach',
  },
  {
    id: 'professional:marketing',
    name: 'Marketing',
  },
  {
    id: 'professional:matchmakers',
    name: 'Matchmakers',
  },
  {
    id: 'professional:officecleaning',
    name: 'Office Cleaning',
  },
  {
    id: 'professional:payroll',
    name: 'Payroll Services',
  },
  {
    id: 'professional:personalassistants',
    name: 'Personal Assistants',
  },
  {
    id: 'professional:privateinvestigation',
    name: 'Private Investigation',
  },
  {
    id: 'professional:publicrelations',
    name: 'Public Relations',
  },
  {
    id: 'professional:talentagencies',
    name: 'Talent Agencies',
  },
  {
    id: 'professional:taxidermy',
    name: 'Taxidermy',
  },
  {
    id: 'professional:translationservices',
    name: 'Translation Services',
  },
  {
    id: 'professional:videofilmproductions',
    name: 'Video/Film Production',
  },
  {
    id: 'professional:web_design',
    name: 'Web Design',
  },
  {
    id: 'professional:signshop',
    name: 'Sign Shop',
  },
  {
    id: 'publicservicesgovt',
    name: 'Public Services & Government',
  },
  {
    id: 'publicservicesgovt:courthouses',
    name: 'Courthouses',
  },
  {
    id: 'publicservicesgovt:departmentsofmotorvehicles',
    name: 'Departments of Motor Vehicles',
  },
  {
    id: 'publicservicesgovt:embassy',
    name: 'Embassy',
  },
  {
    id: 'publicservicesgovt:firedepartments',
    name: 'Fire Departments',
  },
  {
    id: 'publicservicesgovt:landmarks',
    name: 'Landmarks & Historical Buildings',
  },
  {
    id: 'publicservicesgovt:libraries',
    name: 'Libraries',
  },
  {
    id: 'publicservicesgovt:policedepartments',
    name: 'Police Departments',
  },
  {
    id: 'publicservicesgovt:postoffices',
    name: 'Post Offices',
  },
  {
    id: 'realestate',
    name: 'Real Estate',
  },
  {
    id: 'realestate:apartments',
    name: 'Apartments',
  },
  {
    id: 'realestate:commercialrealestate',
    name: 'Commercial Real Estate',
  },
  {
    id: 'realestate:homestaging',
    name: 'Home Staging',
  },
  {
    id: 'realestate:mortgagebrokers',
    name: 'Mortgage Brokers',
  },
  {
    id: 'realestate:propertymgmt',
    name: 'Property Management',
  },
  {
    id: 'realestate:realestateagents',
    name: 'Real Estate Agents',
  },
  {
    id: 'realestate:realestatesvcs',
    name: 'Real Estate Services',
  },
  {
    id: 'realestate:sharedofficespaces',
    name: 'Shared Office Spaces',
  },
  {
    id: 'realestate:university_housing',
    name: 'University Housing',
  },
  {
    id: 'religiousorgs',
    name: 'Religious Organizations',
  },
  {
    id: 'religiousorgs:buddhist_temples',
    name: 'Buddhist Temples',
  },
  {
    id: 'religiousorgs:churches',
    name: 'Churches',
  },
  {
    id: 'religiousorgs:hindu_temples',
    name: 'Hindu Temples',
  },
  {
    id: 'religiousorgs:mosques',
    name: 'Mosques',
  },
  {
    id: 'religiousorgs:synagogues',
    name: 'Synagogues',
  },
  {
    id: 'restaurants',
    name: 'Restaurants',
  },
  {
    id: 'restaurants:afghani',
    name: 'Afghan',
  },
  {
    id: 'restaurants:african',
    name: 'African',
  },
  {
    id: 'restaurants:african:senegalese',
    name: 'Senegalese',
  },
  {
    id: 'restaurants:african:southafrican',
    name: 'South African',
  },
  {
    id: 'restaurants:newamerican',
    name: 'American (New)',
  },
  {
    id: 'restaurants:tradamerican',
    name: 'American (Traditional)',
  },
  {
    id: 'restaurants:arabian',
    name: 'Arabian',
  },
  {
    id: 'restaurants:argentine',
    name: 'Argentine',
  },
  {
    id: 'restaurants:armenian',
    name: 'Armenian',
  },
  {
    id: 'restaurants:asianfusion',
    name: 'Asian Fusion',
  },
  {
    id: 'restaurants:australian',
    name: 'Australian',
  },
  {
    id: 'restaurants:austrian',
    name: 'Austrian',
  },
  {
    id: 'restaurants:bangladeshi',
    name: 'Bangladeshi',
  },
  {
    id: 'restaurants:bbq',
    name: 'Barbeque',
  },
  {
    id: 'restaurants:basque',
    name: 'Basque',
  },
  {
    id: 'restaurants:belgian',
    name: 'Belgian',
  },
  {
    id: 'restaurants:brasseries',
    name: 'Brasseries',
  },
  {
    id: 'restaurants:brazilian',
    name: 'Brazilian',
  },
  {
    id: 'restaurants:breakfast_brunch',
    name: 'Breakfast & Brunch',
  },
  {
    id: 'restaurants:british',
    name: 'British',
  },
  {
    id: 'restaurants:buffets',
    name: 'Buffets',
  },
  {
    id: 'restaurants:burgers',
    name: 'Burgers',
  },
  {
    id: 'restaurants:burmese',
    name: 'Burmese',
  },
  {
    id: 'restaurants:cafes',
    name: 'Cafes',
  },
  {
    id: 'restaurants:cafeteria',
    name: 'Cafeteria',
  },
  {
    id: 'restaurants:cajun',
    name: 'Cajun/Creole',
  },
  {
    id: 'restaurants:cambodian',
    name: 'Cambodian',
  },
  {
    id: 'restaurants:caribbean',
    name: 'Caribbean',
  },
  {
    id: 'restaurants:caribbean:dominican',
    name: 'Dominican',
  },
  {
    id: 'restaurants:caribbean:haitian',
    name: 'Haitian',
  },
  {
    id: 'restaurants:caribbean:puertorican',
    name: 'Puerto Rican',
  },
  {
    id: 'restaurants:caribbean:trinidadian',
    name: 'Trinidadian',
  },
  {
    id: 'restaurants:catalan',
    name: 'Catalan',
  },
  {
    id: 'restaurants:cheesesteaks',
    name: 'Cheesesteaks',
  },
  {
    id: 'restaurants:chicken_wings',
    name: 'Chicken Wings',
  },
  {
    id: 'restaurants:chinese',
    name: 'Chinese',
  },
  {
    id: 'restaurants:chinese:cantonese',
    name: 'Cantonese',
  },
  {
    id: 'restaurants:chinese:dimsum',
    name: 'Dim Sum',
  },
  {
    id: 'restaurants:chinese:shanghainese',
    name: 'Shanghainese',
  },
  {
    id: 'restaurants:chinese:szechuan',
    name: 'Szechuan',
  },
  {
    id: 'restaurants:comfortfood',
    name: 'Comfort Food',
  },
  {
    id: 'restaurants:creperies',
    name: 'Creperies',
  },
  {
    id: 'restaurants:cuban',
    name: 'Cuban',
  },
  {
    id: 'restaurants:czech',
    name: 'Czech',
  },
  {
    id: 'restaurants:delis',
    name: 'Delis',
  },
  {
    id: 'restaurants:diners',
    name: 'Diners',
  },
  {
    id: 'restaurants:ethiopian',
    name: 'Ethiopian',
  },
  {
    id: 'restaurants:hotdogs',
    name: 'Fast Food',
  },
  {
    id: 'restaurants:filipino',
    name: 'Filipino',
  },
  {
    id: 'restaurants:fishnchips',
    name: 'Fish & Chips',
  },
  {
    id: 'restaurants:fondue',
    name: 'Fondue',
  },
  {
    id: 'restaurants:food_court',
    name: 'Food Court',
  },
  {
    id: 'restaurants:foodstands',
    name: 'Food Stands',
  },
  {
    id: 'restaurants:french',
    name: 'French',
  },
  {
    id: 'restaurants:gastropubs',
    name: 'Gastropubs',
  },
  {
    id: 'restaurants:german',
    name: 'German',
  },
  {
    id: 'restaurants:gluten_free',
    name: 'Gluten-Free',
  },
  {
    id: 'restaurants:greek',
    name: 'Greek',
  },
  {
    id: 'restaurants:halal',
    name: 'Halal',
  },
  {
    id: 'restaurants:hawaiian',
    name: 'Hawaiian',
  },
  {
    id: 'restaurants:himalayan',
    name: 'Himalayan/Nepalese',
  },
  {
    id: 'restaurants:hotdog',
    name: 'Hot Dogs',
  },
  {
    id: 'restaurants:hotpot',
    name: 'Hot Pot',
  },
  {
    id: 'restaurants:hungarian',
    name: 'Hungarian',
  },
  {
    id: 'restaurants:iberian',
    name: 'Iberian',
  },
  {
    id: 'restaurants:indpak',
    name: 'Indian',
  },
  {
    id: 'restaurants:indonesian',
    name: 'Indonesian',
  },
  {
    id: 'restaurants:irish',
    name: 'Irish',
  },
  {
    id: 'restaurants:italian',
    name: 'Italian',
  },
  {
    id: 'restaurants:japanese',
    name: 'Japanese',
  },
  {
    id: 'restaurants:korean',
    name: 'Korean',
  },
  {
    id: 'restaurants:kosher',
    name: 'Kosher',
  },
  {
    id: 'restaurants:laotian',
    name: 'Laotian',
  },
  {
    id: 'restaurants:latin',
    name: 'Latin American',
  },
  {
    id: 'restaurants:latin:colombian',
    name: 'Colombian',
  },
  {
    id: 'restaurants:latin:salvadoran',
    name: 'Salvadoran',
  },
  {
    id: 'restaurants:latin:venezuelan',
    name: 'Venezuelan',
  },
  {
    id: 'restaurants:raw_food',
    name: 'Live/Raw Food',
  },
  {
    id: 'restaurants:malaysian',
    name: 'Malaysian',
  },
  {
    id: 'restaurants:mediterranean',
    name: 'Mediterranean',
  },
  {
    id: 'restaurants:mexican',
    name: 'Mexican',
  },
  {
    id: 'restaurants:mideastern',
    name: 'Middle Eastern',
  },
  {
    id: 'restaurants:mideastern:egyptian',
    name: 'Egyptian',
  },
  {
    id: 'restaurants:mideastern:lebanese',
    name: 'Lebanese',
  },
  {
    id: 'restaurants:modern_european',
    name: 'Modern European',
  },
  {
    id: 'restaurants:mongolian',
    name: 'Mongolian',
  },
  {
    id: 'restaurants:moroccan',
    name: 'Moroccan',
  },
  {
    id: 'restaurants:pakistani',
    name: 'Pakistani',
  },
  {
    id: 'restaurants:persian',
    name: 'Persian/Iranian',
  },
  {
    id: 'restaurants:peruvian',
    name: 'Peruvian',
  },
  {
    id: 'restaurants:pizza',
    name: 'Pizza',
  },
  {
    id: 'restaurants:polish',
    name: 'Polish',
  },
  {
    id: 'restaurants:portuguese',
    name: 'Portuguese',
  },
  {
    id: 'restaurants:russian',
    name: 'Russian',
  },
  {
    id: 'restaurants:salad',
    name: 'Salad',
  },
  {
    id: 'restaurants:sandwiches',
    name: 'Sandwiches',
  },
  {
    id: 'restaurants:scandinavian',
    name: 'Scandinavian',
  },
  {
    id: 'restaurants:scottish',
    name: 'Scottish',
  },
  {
    id: 'restaurants:seafood',
    name: 'Seafood',
  },
  {
    id: 'restaurants:singaporean',
    name: 'Singaporean',
  },
  {
    id: 'restaurants:slovakian',
    name: 'Slovakian',
  },
  {
    id: 'restaurants:soulfood',
    name: 'Soul Food',
  },
  {
    id: 'restaurants:soup',
    name: 'Soup',
  },
  {
    id: 'restaurants:southern',
    name: 'Southern',
  },
  {
    id: 'restaurants:spanish',
    name: 'Spanish',
  },
  {
    id: 'restaurants:steak',
    name: 'Steakhouses',
  },
  {
    id: 'restaurants:sushi',
    name: 'Sushi Bars',
  },
  {
    id: 'restaurants:taiwanese',
    name: 'Taiwanese',
  },
  {
    id: 'restaurants:tapas',
    name: 'Tapas Bars',
  },
  {
    id: 'restaurants:tapasmallplates',
    name: 'Tapas/Small Plates',
  },
  {
    id: 'restaurants:tex_mex',
    name: 'Tex-Mex',
  },
  {
    id: 'restaurants:thai',
    name: 'Thai',
  },
  {
    id: 'restaurants:turkish',
    name: 'Turkish',
  },
  {
    id: 'restaurants:ukrainian',
    name: 'Ukrainian',
  },
  {
    id: 'restaurants:vegan',
    name: 'Vegan',
  },
  {
    id: 'restaurants:vegetarian',
    name: 'Vegetarian',
  },
  {
    id: 'restaurants:vietnamese',
    name: 'Vietnamese',
  },
  {
    id: 'shopping',
    name: 'Shopping',
  },
  {
    id: 'shopping:adult',
    name: 'Adult',
  },
  {
    id: 'shopping:antiques',
    name: 'Antiques',
  },
  {
    id: 'shopping:galleries',
    name: 'Art Galleries',
  },
  {
    id: 'shopping:artsandcrafts',
    name: 'Arts & Crafts',
  },
  {
    id: 'shopping:artsandcrafts:artsupplies',
    name: 'Art Supplies',
  },
  {
    id: 'shopping:artsandcrafts:stationery',
    name: 'Cards & Stationery',
  },
  {
    id: 'shopping:artsandcrafts:costumes',
    name: 'Costumes',
  },
  {
    id: 'shopping:artsandcrafts:fabricstores',
    name: 'Fabric Stores',
  },
  {
    id: 'shopping:artsandcrafts:framing',
    name: 'Framing',
  },
  {
    id: 'shopping:auctionhouses',
    name: 'Auction Houses',
  },
  {
    id: 'shopping:baby_gear',
    name: 'Baby Gear & Furniture',
  },
  {
    id: 'shopping:bespoke',
    name: 'Bespoke Clothing',
  },
  {
    id: 'shopping:media',
    name: 'Books, Mags, Music & Video',
  },
  {
    id: 'shopping:media:bookstores',
    name: 'Bookstores',
  },
  {
    id: 'shopping:media:comicbooks',
    name: 'Comic Books',
  },
  {
    id: 'shopping:media:musicvideo',
    name: 'Music & DVDs',
  },
  {
    id: 'shopping:media:mags',
    name: 'Newspapers & Magazines',
  },
  {
    id: 'shopping:media:videoandgames',
    name: 'Videos & Video Game Rental',
  },
  {
    id: 'shopping:media:vinyl_records',
    name: 'Vinyl Records',
  },
  {
    id: 'shopping:bridal',
    name: 'Bridal',
  },
  {
    id: 'shopping:computers',
    name: 'Computers',
  },
  {
    id: 'shopping:cosmetics',
    name: 'Cosmetics & Beauty Supply',
  },
  {
    id: 'shopping:deptstores',
    name: 'Department Stores',
  },
  {
    id: 'shopping:discountstore',
    name: 'Discount Store',
  },
  {
    id: 'shopping:drugstores',
    name: 'Drugstores',
  },
  {
    id: 'shopping:electronics',
    name: 'Electronics',
  },
  {
    id: 'shopping:opticians',
    name: 'Eyewear & Opticians',
  },
  {
    id: 'shopping:fashion',
    name: 'Fashion',
  },
  {
    id: 'shopping:fashion:accessories',
    name: 'Accessories',
  },
  {
    id: 'shopping:fashion:childcloth',
    name: "Children's Clothing",
  },
  {
    id: 'shopping:fashion:deptstores',
    name: 'Department Stores',
  },
  {
    id: 'shopping:fashion:formalwear',
    name: 'Formal Wear',
  },
  {
    id: 'shopping:fashion:hats',
    name: 'Hats',
  },
  {
    id: 'shopping:fashion:leather',
    name: 'Leather Goods',
  },
  {
    id: 'shopping:fashion:lingerie',
    name: 'Lingerie',
  },
  {
    id: 'shopping:fashion:maternity',
    name: 'Maternity Wear',
  },
  {
    id: 'shopping:fashion:menscloth',
    name: "Men's Clothing",
  },
  {
    id: 'shopping:fashion:plus_size_fashion',
    name: 'Plus Size Fashion',
  },
  {
    id: 'shopping:fashion:shoes',
    name: 'Shoe Stores',
  },
  {
    id: 'shopping:fashion:sportswear',
    name: 'Sports Wear',
  },
  {
    id: 'shopping:fashion:surfshop',
    name: 'Surf Shop',
  },
  {
    id: 'shopping:fashion:swimwear',
    name: 'Swimwear',
  },
  {
    id: 'shopping:fashion:vintage',
    name: 'Used, Vintage & Consignment',
  },
  {
    id: 'shopping:fashion:womenscloth',
    name: "Women's Clothing",
  },
  {
    id: 'shopping:fireworks',
    name: 'Fireworks',
  },
  {
    id: 'shopping:fleamarkets',
    name: 'Flea Markets',
  },
  {
    id: 'shopping:flowers',
    name: 'Flowers & Gifts',
  },
  {
    id: 'shopping:flowers:stationery',
    name: 'Cards & Stationery',
  },
  {
    id: 'shopping:flowers:florists',
    name: 'Florists',
  },
  {
    id: 'shopping:flowers:giftshops',
    name: 'Gift Shops',
  },
  {
    id: 'shopping:golfshops',
    name: 'Golf Equipment Shops',
  },
  {
    id: 'shopping:guns_and_ammo',
    name: 'Guns & Ammo',
  },
  {
    id: 'shopping:hobbyshops',
    name: 'Hobby Shops',
  },
  {
    id: 'shopping:homeandgarden',
    name: 'Home & Garden',
  },
  {
    id: 'shopping:homeandgarden:appliances',
    name: 'Appliances',
  },
  {
    id: 'shopping:homeandgarden:furniture',
    name: 'Furniture Stores',
  },
  {
    id: 'shopping:homeandgarden:hardware',
    name: 'Hardware Stores',
  },
  {
    id: 'shopping:homeandgarden:homedecor',
    name: 'Home Decor',
  },
  {
    id: 'shopping:homeandgarden:hottubandpool',
    name: 'Hot Tub & Pool',
  },
  {
    id: 'shopping:homeandgarden:kitchenandbath',
    name: 'Kitchen & Bath',
  },
  {
    id: 'shopping:homeandgarden:mattresses',
    name: 'Mattresses',
  },
  {
    id: 'shopping:homeandgarden:gardening',
    name: 'Nurseries & Gardening',
  },
  {
    id: 'shopping:jewelry',
    name: 'Jewelry',
  },
  {
    id: 'shopping:knittingsupplies',
    name: 'Knitting Supplies',
  },
  {
    id: 'shopping:luggage',
    name: 'Luggage',
  },
  {
    id: 'shopping:medicalsupplies',
    name: 'Medical Supplies',
  },
  {
    id: 'shopping:mobilephones',
    name: 'Mobile Phones',
  },
  {
    id: 'shopping:motorcyclinggear',
    name: 'Motorcycle Gear',
  },
  {
    id: 'shopping:musicalinstrumentsandteachers',
    name: 'Musical Instruments & Teachers',
  },
  {
    id: 'shopping:officeequipment',
    name: 'Office Equipment',
  },
  {
    id: 'shopping:outlet_stores',
    name: 'Outlet Stores',
  },
  {
    id: 'shopping:pawn',
    name: 'Pawn Shops',
  },
  {
    id: 'shopping:personal_shopping',
    name: 'Personal Shopping',
  },
  {
    id: 'shopping:photographystores',
    name: 'Photography Stores & Services',
  },
  {
    id: 'shopping:poolbilliards',
    name: 'Pool & Billiards',
  },
  {
    id: 'shopping:popupshops',
    name: 'Pop-up Shops',
  },
  {
    id: 'shopping:shoppingcenters',
    name: 'Shopping Centers',
  },
  {
    id: 'shopping:sportgoods',
    name: 'Sporting Goods',
  },
  {
    id: 'shopping:sportgoods:bikes',
    name: 'Bikes',
  },
  {
    id: 'shopping:sportgoods:golfequipment',
    name: 'Golf Equipment',
  },
  {
    id: 'shopping:sportgoods:outdoorgear',
    name: 'Outdoor Gear',
  },
  {
    id: 'shopping:sportgoods:sportswear',
    name: 'Sports Wear',
  },
  {
    id: 'shopping:thrift_stores',
    name: 'Thrift Stores',
  },
  {
    id: 'shopping:tobaccoshops',
    name: 'Tobacco Shops',
  },
  {
    id: 'shopping:toys',
    name: 'Toy Stores',
  },
  {
    id: 'shopping:trophyshops',
    name: 'Trophy Shops',
  },
  {
    id: 'shopping:uniforms',
    name: 'Uniforms',
  },
  {
    id: 'shopping:watches',
    name: 'Watches',
  },
  {
    id: 'shopping:wholesale_stores',
    name: 'Wholesale Stores',
  },
  {
    id: 'shopping:wigs',
    name: 'Wigs',
  },
  {
    id: 'transportation',
    name: 'Transportation',
  },
  {
    id: 'transportation:air',
    name: 'Air',
  },
  {
    id: 'transportation:railway',
    name: 'Railway',
  },
  {
    id: 'transportation:road',
    name: 'Road',
  },
  {
    id: 'transportation:water',
    name: 'Water',
  },
];

export const TAXONOMY_OPTIONS_TOKEN = 'TAXONOMY_OPTIONS_TOKEN';
export function getTaxonomyOptions(): Observable<Taxonomy[]> {
  return of(TAXONOMY_OPTIONS);
}
