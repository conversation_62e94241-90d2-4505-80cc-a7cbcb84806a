import { Salesperson as SSCSalesperson } from '@galaxy/types';
import { schedule } from '@vendasta/rx-utils';
import { WorkStates } from '@vendasta/rx-utils/work-state';
import { BusinessSearchResponse } from '@vendasta/sales';
import {
  ListOpportunitiesResponse,
  Opportunity,
  OpportunityCount,
  SalesOpportunitiesSdk,
} from '@vendasta/sales-opportunities';
import { Salesperson } from '@vendasta/salesperson';
import { EMPTY, Observable, ReplaySubject, of, throwError } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { TestScheduler } from 'rxjs/testing';
import { SalesPersonAction } from '../account-history/account-history.component';
import { IdentityMockTranslateService, MockTranslateService } from '../common/translate.mock';
import { LoggedInUserInfo } from '../logged-in-user-info';
import { EMPTY_STATE } from '../rx-utils/state-maps';
import { SalesStatusFilterId } from './manage-accounts-filters';
import { ManageAccountsFiltersService } from './manage-accounts-filters/manage-accounts-filters.service';
import { ManageAccountsUiService, formatBusinessAddress } from './manage-accounts-ui.service';
import {
  AccountGoal,
  AccountTag,
  Business,
  BusinessLoader,
  LoadBusinessEvent,
  LoadMoreValues,
} from './manage-accounts.service';
import { mockLoadMoreValues } from './manage-accounts.service.spec';

let sched: TestScheduler;

const mockLoggedInUser = {
  salespersonId: 'UID-456',
  isImpersonation: false,
  hasAccessToAllAccountsInMarket: true,
  partnerId: 'pid',
  marketId: 'market',
};

const mockAccountTag = {
  name: 'tag1',
  value: 'tag2',
};

const mockAccountGoal = {
  name: 'goal name',
  value: 'goal value',
  description: 'goal description',
};

class MockSalesOpportunitiesService {
  countResponse: OpportunityCount[];

  constructor(countResp?: OpportunityCount[]) {
    this.countResponse = countResp ? countResp : [];
  }

  addAttachment(): Observable<any> {
    return throwError('implement me');
  }

  get(): Observable<Opportunity> {
    return throwError('implement me');
  }

  getOpportunityCountByAccountGroups(): Observable<OpportunityCount[]> {
    return sched.createColdObservable('-x', { x: this.countResponse });
  }

  list(): Observable<ListOpportunitiesResponse> {
    return throwError('implement me');
  }
}

export const bus1 = (<Business>{
  salesPersonId: 'UID-123',
  accountGroupId: 'AG-5RRLBLBFGL',
  partnerId: 'VUNI',
  marketId: 'default',
  name: "Bill's burgers",
  hotness: 3,
  city: 'Saskatoon',
  zip: 'S7N2E4',
  address: "125 Bob's St",
  state: 'SK',
  country: 'Canada',
  phoneNumber: '(*************',
  lastSalesActivityDate: new Date('2018-10-12T14:46:26Z'),
  lastCustomerActivityDate: new Date('2018-10-12T14:45:22.000Z'),
  salesStatus: SalesStatusFilterId.FOLLOW_UP_NEEDED,
  salesStatusTag: SalesStatusFilterId.FOLLOW_UP_NEEDED,
  isRead: false,
  salesPersonAction: SalesPersonAction.EMAIL_SENT,
  latestSnapshotExpiry: new Date('2018-10-19T14:45:41Z'),
  snapshotOrEmailCampaignStatus: false,
  activityType: null,
  lastConnectedDate: null,
}) as Business;
export const bus2 = (<Business>{
  salesPersonId: 'UID-456',
  accountGroupId: 'AG-6CHBZWSR68',
  partnerId: 'VUNI',
  marketId: 'default',
  name: "Bob's burgers",
  hotness: 0,
  city: 'Saskatoon',
  zip: 'S7N2E4',
  address: "123 Bob's St",
  state: 'SK',
  country: 'Canada',
  phoneNumber: '(*************',
  lastSalesActivityDate: new Date('2018-10-11T17:07:27Z'),
  lastCustomerActivityDate: new Date('2018-10-11T17:04:50.000Z'),
  salesStatus: SalesStatusFilterId.IN_PROGRESS,
  salesStatusTag: SalesStatusFilterId.IN_PROGRESS,
  isRead: false,
  salesPersonAction: SalesPersonAction.EMAIL_SENT,
  latestSnapshotExpiry: new Date('2018-10-18T17:05:01Z'),
  snapshotOrEmailCampaignStatus: false,
  activityType: null,
  lastConnectedDate: new Date(),
}) as Business;

class MockManageAccountsService implements BusinessLoader {
  salesPerson$: Observable<LoggedInUserInfo>;
  totalResults$: Observable<number>;
  businessState$: WorkStates<BusinessSearchResponse>;
  moreBusinessState$: WorkStates<Business[]>;
  loadMoreValues$: Observable<LoadMoreValues>;
  businessResult$: Observable<Business[]>;
  searchingWithEmptyTeam$: Observable<boolean>;

  constructor(loadMore = false, businesses: Business[] = [bus1, bus2]) {
    this.businessState$ = {
      isLoading$: undefined,
      isSuccess$: undefined,
      successEvents$: undefined,
      workResults$: sched.createColdObservable('-x', {
        x: { businesses: businesses, pagingMetadata: null } as unknown as BusinessSearchResponse,
      }),
    };
    this.moreBusinessState$ = {
      isLoading$: undefined,
      isSuccess$: undefined,
      successEvents$: undefined,
      workResults$: sched.createColdObservable('-x', { x: [] }),
    };

    this.businessResult$ = loadMore
      ? this.moreBusinessState$.workResults$
      : this.businessState$.workResults$.pipe(map((r) => r.businesses as unknown as Business[]));

    this.salesPerson$ = sched.createColdObservable('-x', { x: mockLoggedInUser as any });
    this.loadMoreValues$ = sched.createColdObservable('-x', { x: mockLoadMoreValues });
    this.searchingWithEmptyTeam$ = sched.createColdObservable('-tfttfft', { t: true, f: false });
  }

  initLoadBusinesses(): void {
    throw new Error('Method not implemented.');
  }

  loadMoreBusinesses(): void {
    throw new Error('Method not implemented.');
  }

  getAccountTags(): Observable<AccountTag[]> {
    return sched.createColdObservable('-x', { x: [mockAccountTag] });
  }

  getGoals(): Observable<AccountGoal[]> {
    return sched.createColdObservable('-x', { x: [mockAccountGoal] });
  }
}

class MockAtlasLanguageService {
  language$ = sched.createColdObservable('x', { x: 'en' });
}

const mockSalesPeople: Salesperson[] = [
  {
    salespersonId: 'UID-123',
    firstName: 'Mr',
    lastName: 'Salesperson',
    partnerId: '',
    marketId: '',
    email: '',
    fullName: 'Mr Salesperson',
  },
  {
    salespersonId: 'UID-456',
    firstName: 'Ron',
    lastName: 'Swanson',
    partnerId: '',
    marketId: '',
    email: '',
    fullName: 'Ron Swanson',
  },
];

describe('_formatBusinessAddress', () => {
  it('returns empty string if no values given', () => {
    const actual = formatBusinessAddress(null, null, null, null);

    const expected = '';
    expect(actual).toBe(expected);
  });
  it('returns empty string if all values given are empty', () => {
    const address = null;
    const city = '';
    const state = undefined;

    const actual = formatBusinessAddress(address, city, state, null);

    const expected = '';
    expect(actual).toBe(expected);
  });
  it('formats address correctly for non-empty values', () => {
    const address = '101 First Ave.';
    const city = 'Saskatoon';
    const state = 'SK';

    const actual = formatBusinessAddress(address, city, state, null);

    const expected = '101 First Ave.<br>Saskatoon, SK';
    expect(actual).toBe(expected);
  });
  it('formats address correctly for a mix of empty and non-empty values', () => {
    const address = undefined;
    const city = '';
    const state = 'SK';
    const zip = 'S0L 2V0';

    const actual = formatBusinessAddress(address, city, state, zip);

    const expected = 'SK, S0L 2V0';
    expect(actual).toBe(expected);
  });
});

describe('ManageAccountsUiService', () => {
  let service: ManageAccountsUiService;
  let mockManageAccountsService: MockManageAccountsService;
  let mockSalesOpportunitiesSdk: SalesOpportunitiesSdk;
  let mockLanguageService: MockAtlasLanguageService;
  let allSalespeople$: Observable<SSCSalesperson[]>;
  let manageAccountsFiltersService: ManageAccountsFiltersService;

  beforeEach(() => {
    sched = new TestScheduler((a, b) => expect(a).toEqual(b));
    allSalespeople$ = sched.createColdObservable('-x', {
      x: mockSalesPeople.map((sp) => SSCSalesperson.fromCoreSalesperson(sp)),
    });
    mockLanguageService = new MockAtlasLanguageService();
  });
  afterEach(() => {
    sched.flush();
  });

  describe('on init', () => {
    beforeEach(() => {
      mockManageAccountsService = new MockManageAccountsService();
      mockSalesOpportunitiesSdk = new MockSalesOpportunitiesService() as any;
      manageAccountsFiltersService = new ManageAccountsFiltersService(
        of(''),
        of([]),
        of([]),
        of([]),
        of([]),
        of([]),
        of([]),
      );
      const mockUserInfo$ = of(<LoggedInUserInfo>{ partnerId: 'PID-123', marketId: 'MKT-123' });

      service = new ManageAccountsUiService(
        mockManageAccountsService as any,
        allSalespeople$,
        mockSalesOpportunitiesSdk,
        mockUserInfo$,
        new MockTranslateService() as any,
        mockLanguageService as any,
        manageAccountsFiltersService,
        null,
      );
    });
    it('should load "days since" strings', () => {
      const daysSinceCustomerActivity$ = service.businesses$.pipe(
        switchMap((businesses) => businesses[0].daysSinceLastCustomerActivity$),
      );

      sched.expectObservable(daysSinceCustomerActivity$).toBe('-x', {
        x: (<any>expect).any(String),
      });
    });
    it('should load the css color for the sales status', () => {
      const salesStatusColors$ = service.businesses$.pipe(
        map((businesses) => businesses.map((biz) => biz.salesStatusColor)),
      );
      sched.expectObservable(salesStatusColors$).toBe('-x', { x: ['yellow', 'purple'] });
    });
    it('should create a hotness array that is as long as the business is hot', () => {
      const hotnesses$ = service.businesses$.pipe(map((businesses) => businesses.map((biz) => biz.flames)));
      sched.expectObservable(hotnesses$).toBe('-x', { x: [['🔥', '🔥', '🔥'], []] });
    });

    it("should load the salespeople's names into the UiBusiness", () => {
      const salesPersonOne$ = service.businesses$.pipe(switchMap((businesses) => businesses[0].salesPersonName$));
      const salesPersonTwo$ = service.businesses$.pipe(switchMap((businesses) => businesses[1].salesPersonName$));

      sched.expectObservable(salesPersonOne$).toBe('--x', { x: 'Mr Salesperson' });
      sched.expectObservable(salesPersonTwo$).toBe('--x', { x: 'Ron Swanson' });
    });
    it('should load the salespeople', () => {
      sched.expectObservable(service.salesPeople$).toBe('-x', { x: mockSalesPeople });
    });
  });

  describe('businesses$', () => {
    beforeEach(() => {
      manageAccountsFiltersService = new ManageAccountsFiltersService(
        of(''),
        of([]),
        of([]),
        of([]),
        of([]),
        of([]),
        of([]),
      );
    });
    it('should contain opportunity count for business with 1 open opportunity', () => {
      const mockBusinessesResp: Business[] = [
        {
          name: 'Foo Bar',
          accountGroupId: 'AG-123',
          lastSalesActivityDate: new Date('2018-10-12T14:46:26.000Z'),
          lastCustomerActivityDate: new Date('2018-10-12T14:45:22.000Z'),
        } as Business,
      ];
      const mockOppCount: OpportunityCount[] = [new OpportunityCount({ accountGroupId: 'AG-123', count: 1 })];
      const manageAccts = new MockManageAccountsService(false, mockBusinessesResp);
      const salesOppsSdk = new MockSalesOpportunitiesService(mockOppCount);
      const mockUserInfo$ = of(<LoggedInUserInfo>{ partnerId: 'PID-123', marketId: 'MKT-123' });
      service = new ManageAccountsUiService(
        manageAccts as any,
        allSalespeople$,
        salesOppsSdk as any,
        mockUserInfo$,
        new IdentityMockTranslateService() as any,
        mockLanguageService as any,
        manageAccountsFiltersService,
        null,
      );

      const actual$ = service.businesses$.pipe(switchMap((businesses) => businesses[0].opportunitiesTagFormatted$));

      const expected = 'MANAGE_ACCOUNTS.OPEN_OPPORTUNITIES_SINGULAR';
      sched.expectObservable(actual$).toBe('--x', { x: expected });
    });
    it('should contain opportunity count for business with more than 1 open opportunity', () => {
      const mockBusinessesResp: Business[] = [
        {
          name: 'Foo Bar',
          accountGroupId: 'AG-123',
          lastSalesActivityDate: new Date('2018-10-12T14:46:26.000Z'),
          lastCustomerActivityDate: new Date('2018-10-12T14:45:22.000Z'),
        } as Business,
      ];
      const mockOppCount: OpportunityCount[] = [new OpportunityCount({ accountGroupId: 'AG-123', count: 2 })];
      const manageAccts = new MockManageAccountsService(false, mockBusinessesResp);
      const salesOppsSdk = new MockSalesOpportunitiesService(mockOppCount);
      const mockUserInfo$ = of(<LoggedInUserInfo>{ partnerId: 'PID-123', marketId: 'MKT-123' });
      service = new ManageAccountsUiService(
        manageAccts as any,
        allSalespeople$,
        salesOppsSdk as any,
        mockUserInfo$,
        new IdentityMockTranslateService() as any,
        mockLanguageService as any,
        manageAccountsFiltersService,
        null,
      );

      const actual$ = service.businesses$.pipe(switchMap((businesses) => businesses[0].opportunitiesTagFormatted$));

      const expected = 'MANAGE_ACCOUNTS.OPEN_OPPORTUNITIES_MULTIPLE';
      sched.expectObservable(actual$).toBe('--x', { x: expected });
    });
    it('should not contain opportunity count for business without open opportunities', () => {
      const mockBusinessesResp: Business[] = [
        {
          name: 'Foo Bar',
          accountGroupId: 'AG-123',
          lastSalesActivityDate: new Date('2018-10-12T14:46:26.000Z'),
          lastCustomerActivityDate: new Date('2018-10-12T14:45:22.000Z'),
        } as any,
      ];
      const mockOppCount: OpportunityCount[] = [new OpportunityCount({ accountGroupId: 'AG-123' })];
      const manageAccts = new MockManageAccountsService(false, mockBusinessesResp);
      const salesOppsSdk = new MockSalesOpportunitiesService(mockOppCount);
      service = new ManageAccountsUiService(
        manageAccts as any,
        allSalespeople$,
        salesOppsSdk as any,
        EMPTY,
        new IdentityMockTranslateService() as any,
        mockLanguageService as any,
        manageAccountsFiltersService,
        null,
      );

      const actual$ = service.businesses$.pipe(switchMap((businesses) => businesses[0].opportunitiesTagFormatted$));

      sched.expectObservable(actual$).toBe('');
    });
  });

  describe('updateSortValue', () => {
    beforeEach(() => {
      manageAccountsFiltersService = new ManageAccountsFiltersService(
        of(''),
        of([]),
        of([]),
        of([]),
        of([]),
        of([]),
        of([]),
      );
    });
    it('should request a load with the given sort value', () => {
      const requests = new ReplaySubject<LoadBusinessEvent>(1);
      const loader: BusinessLoader = {
        businessState$: EMPTY_STATE,
        salesPerson$: EMPTY,
        moreBusinessState$: EMPTY_STATE,
        businessResult$: EMPTY_STATE.workResults$,
        loadMoreValues$: of({
          nextCursor: '',
          loadMore: false,
        }),
        searchingWithEmptyTeam$: EMPTY,
        initLoadBusinesses(loadBusinessesEvent: LoadBusinessEvent): void {
          requests.next(loadBusinessesEvent);
        },
        getAccountTags(): Observable<AccountTag[]> {
          return of([]);
        },
        getGoals(): Observable<AccountGoal[]> {
          return of([]);
        },
      } as any;
      service = new ManageAccountsUiService(
        loader,
        allSalespeople$,
        null,
        EMPTY,
        null,
        null,
        manageAccountsFiltersService,
        null,
      );
      schedule(sched, '-|', () => service.updateSortValue('last-customer-activity'));
      const expectedEvent: LoadBusinessEvent = { sortValue: 'last-activity-timestamp' };
      sched.expectObservable(requests).toBe('-x', { x: expectedEvent });
    });
  });
  describe('toggleSortDirection', () => {
    beforeEach(() => {
      manageAccountsFiltersService = new ManageAccountsFiltersService(
        of(''),
        of([]),
        of([]),
        of([]),
        of([]),
        of([]),
        of([]),
      );
    });
    it('it should change isSortAscending$ correctly for one toggle', () => {
      const loader: BusinessLoader = {
        businessState$: EMPTY_STATE,
        salesPerson$: EMPTY,
        moreBusinessState$: EMPTY_STATE,
        businessResult$: EMPTY_STATE.workResults$,
        loadMoreValues$: of({
          nextCursor: '',
          loadMore: false,
        }) as any,
        searchingWithEmptyTeam$: EMPTY,
        initLoadBusinesses: () => {
          return;
        },
        loadMoreBusinesses: () => {
          return;
        },
        getAccountTags(): Observable<AccountTag[]> {
          return of([]);
        },
        getGoals(): Observable<AccountGoal[]> {
          return of([]);
        },
      } as any;
      service = new ManageAccountsUiService(
        loader,
        allSalespeople$,
        null,
        EMPTY,
        null,
        null,
        manageAccountsFiltersService,
        null,
      );
      schedule(sched, '-|', () => service.toggleSortDirection());
      sched.expectObservable(service.isSortAscending$).toBe('ft', { t: true, f: false });
    });
    it('it should change isSortAscending$ correctly for two toggles', () => {
      const loader: BusinessLoader = {
        businessState$: EMPTY_STATE,
        salesPerson$: EMPTY,
        moreBusinessState$: EMPTY_STATE,
        businessResult$: EMPTY_STATE.workResults$,
        loadMoreValues$: of({
          nextCursor: '',
          loadMore: false,
        }),
        searchingWithEmptyTeam$: EMPTY,
        initLoadBusinesses: () => {
          return;
        },
        loadMoreBusinesses: () => {
          return;
        },
        getAccountTags(): Observable<AccountTag[]> {
          return of([]);
        },
        getGoals(): Observable<AccountGoal[]> {
          return of([]);
        },
      } as any;
      service = new ManageAccountsUiService(
        loader,
        allSalespeople$,
        null,
        EMPTY,
        null,
        null,
        manageAccountsFiltersService,
        null,
      );
      schedule(sched, '-|', () => service.toggleSortDirection());
      schedule(sched, '--|', () => service.toggleSortDirection());
      sched.expectObservable(service.isSortAscending$).toBe('ftf', { t: true, f: false });
    });
    it('should trigger a load business request with the correct value after one toggle', () => {
      const requests = new ReplaySubject<LoadBusinessEvent>(1);
      const loader: BusinessLoader = {
        businessState$: EMPTY_STATE,
        salesPerson$: EMPTY,
        moreBusinessState$: EMPTY_STATE,
        businessResult$: EMPTY_STATE.workResults$,
        loadMoreValues$: of({
          nextCursor: '',
          loadMore: false,
        }),
        searchingWithEmptyTeam$: EMPTY,
        initLoadBusinesses(loadBusinessesEvent: LoadBusinessEvent): void {
          requests.next(loadBusinessesEvent);
        },
        loadMoreBusinesses: () => {
          return;
        },
        getAccountTags(): Observable<AccountTag[]> {
          return of([]);
        },
        getGoals(): Observable<AccountGoal[]> {
          return of([]);
        },
      } as any;
      const debounceTime = sched.createTime('--|');
      const salesOppsSdk = new MockSalesOpportunitiesService();
      service = new ManageAccountsUiService(
        loader,
        allSalespeople$,
        salesOppsSdk as any,
        EMPTY,
        new IdentityMockTranslateService() as any,
        mockLanguageService as any,
        manageAccountsFiltersService,
        sched,
        debounceTime,
      );

      schedule(sched, '-|', () => service.toggleSortDirection());

      const expectedEvent: LoadBusinessEvent = { sortAscending: true };
      sched.expectObservable(requests).toBe('-' + '--x', { x: expectedEvent });
    });
    it(
      'should trigger a load business request with the correct value after two toggles, ' +
        'ignoring the first toggle because of debouncing',
      () => {
        const requests = new ReplaySubject<LoadBusinessEvent>(1);
        const loader: BusinessLoader = {
          businessState$: EMPTY_STATE,
          salesPerson$: EMPTY,
          moreBusinessState$: EMPTY_STATE,
          businessResult$: EMPTY_STATE.workResults$,
          loadMoreValues$: of({
            nextCursor: '',
            loadMore: false,
          }),
          searchingWithEmptyTeam$: EMPTY,
          initLoadBusinesses(loadBusinessesEvent: LoadBusinessEvent): void {
            requests.next(loadBusinessesEvent);
          },
          loadMoreBusinesses: () => {
            return;
          },
          getAccountTags(): Observable<AccountTag[]> {
            return of([]);
          },
          getGoals(): Observable<AccountGoal[]> {
            return of([]);
          },
        } as any;
        const debounceTime = sched.createTime(`--|`);
        const salesOppsSdk = new MockSalesOpportunitiesService();
        service = new ManageAccountsUiService(
          loader,
          allSalespeople$,
          salesOppsSdk as any,
          EMPTY,
          new IdentityMockTranslateService() as any,
          mockLanguageService as any,
          manageAccountsFiltersService,
          sched,
          debounceTime,
        );

        schedule(sched, '-|', () => service.toggleSortDirection());
        schedule(sched, '--|', () => service.toggleSortDirection());

        const expectedEvent: LoadBusinessEvent = { sortAscending: false };
        sched.expectObservable(requests).toBe('-' + '-' + '--x', { x: expectedEvent });
      },
    );
  });

  describe('SalesPerson$', () => {
    beforeEach(() => {
      manageAccountsFiltersService = new ManageAccountsFiltersService(
        of(''),
        of([]),
        of([]),
        of([]),
        of([]),
        of([]),
        of([]),
      );
    });
    // ManageAccountsUiService.SalesPerson is used instead of SalespeopleService.getCurrent because for the salesperson
    // select filter to work, the "current salesperson" objects must to be equal to the salesperson in the list of
    // salespeople fetched from core.  This could possible be avoided if we stopped using SPs from @vendasta/core.
    it('should only emit once if the salesperson has not changed', () => {
      mockManageAccountsService = new MockManageAccountsService();
      const salespeopleRefreshed$ = sched.createColdObservable('-x-x', {
        x: mockSalesPeople.map((sp) => SSCSalesperson.fromCoreSalesperson(sp)),
      });
      const s = new ManageAccountsUiService(
        mockManageAccountsService as any,
        salespeopleRefreshed$,
        null,
        EMPTY,
        null,
        null,
        manageAccountsFiltersService,
        null,
      );
      sched.expectObservable(s.salesPerson$).toBe('-x------', { x: expect.anything() });
    });
  });
});
