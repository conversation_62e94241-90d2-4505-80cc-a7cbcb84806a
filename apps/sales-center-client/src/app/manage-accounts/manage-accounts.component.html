<glxy-page [pagePadding]="false">
  <glxy-page-toolbar [showExtendedToolbar]="true">
    <glxy-page-title>
      {{ 'MANAGE_ACCOUNTS.TITLE' | translate }}
    </glxy-page-title>
    <glxy-page-actions>
      <ng-container *ngIf="accessPartnerProspects$ | async; else noPartnerProspects">
        <button class="button" mat-stroked-button (click)="goToProspects()">
          {{ 'NAVIGATION.SIDEBAR.PROSPECTS_OPTION_TITLE' | translate }}
        </button>
      </ng-container>

      <ng-template #noPartnerProspects>
        <button class="button" mat-stroked-button (click)="goToAnalytics()">
          {{ 'NAVIGATION.SIDEBAR.PROSPECTS_OPTION_TITLE' | translate }}
        </button>
      </ng-template>

      <ng-container *ngIf="showCustomFieldsFilterLink$ | async">
        <button mat-stroked-button id="custom-fields-filter" (click)="handleFilterCustomFieldsClick()">
          {{ 'MANAGE_ACCOUNTS.FILTER_BY_CUSTOM_FIELDS.BUTTON_TEXT' | translate }}
          <glxy-badge [color]="'blue-solid'">BETA</glxy-badge>
        </button>
      </ng-container>

      <button mat-flat-button color="primary" id="easy-account-create-button" (click)="businessSearchClick()">
        {{ 'MANAGE_ACCOUNTS.CREATE_ACCOUNT_BUTTON_TEXT' | translate }}
      </button>
    </glxy-page-actions>
  </glxy-page-toolbar>

  <app-side-drawer-container>
    <ng-container *ngIf="hasPageData$ | async; else pageLoader">
      <sales-ui-slide-out-panel>
        <ng-container content>
          <div class="accounts-container">
            <glxy-alert type="info" *ngIf="showTeamFilterWarning$ | async">
              <strong>
                {{ 'MANAGE_ACCOUNTS.FILTERING_BY_TEAM_WITHOUT_MEMBERS_INTRO' | translate }}
              </strong>
              &mdash;
              {{ 'MANAGE_ACCOUNTS.FILTERING_BY_TEAM_WITHOUT_MEMBERS' | translate }}
            </glxy-alert>

            <mat-tab-group
              animationDuration="0"
              mat-stretch-tabs="false"
              mat-align-tabs="start"
              (selectedTabChange)="lifecycleTabChanged($event)"
            >
              <mat-tab [label]="'COMMON.ALL' | translate"></mat-tab>
              <mat-tab [label]="'COMMON.LIFECYCLE_STAGES.LEAD' | translate"></mat-tab>
              <mat-tab [label]="'COMMON.LIFECYCLE_STAGES.PROSPECT' | translate"></mat-tab>
              <mat-tab [label]="'COMMON.LIFECYCLE_STAGES.CUSTOMER' | translate"></mat-tab>
            </mat-tab-group>

            <app-manage-accounts-filters #accountFilters [toolbarText$]="toolbarText$">
              <div header id="header-div">
                <ng-container>
                  <button mat-button id="more-options" (click)="openEmailSearchPage()">
                    {{ 'MANAGE_ACCOUNTS.ADVANCED_SEARCH_BUTTON_TEXT' | translate }}
                  </button>
                </ng-container>
                <mat-form-field>
                  <mat-select (selectionChange)="newSort($event)" [(ngModel)]="selectedSortOption">
                    <mat-option value="hotness">
                      {{ 'COMMON.SALES_TERMS.HOTNESS' | translate }}
                    </mat-option>
                    <mat-option value="created">
                      {{ 'COMMON.ATTRIBUTES.CREATED' | translate }}
                    </mat-option>
                    <mat-option value="name">
                      {{ 'COMMON.ATTRIBUTES.NAME' | translate }}
                    </mat-option>
                    <mat-option value="last-customer-activity">
                      {{ 'COMMON.ACCOUNT_ATTRIBUTES.LAST_CUSTOMER_ACTIVITY' | translate }}
                    </mat-option>
                    <mat-option value="last-sales-activity">
                      {{ 'COMMON.ACCOUNT_ATTRIBUTES.LAST_SALES_ACTIVITY' | translate }}
                    </mat-option>
                    <mat-option value="last-connected">
                      {{ 'COMMON.ACCOUNT_ATTRIBUTES.LAST_CONNECTED' | translate }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
                <mat-icon id="sort-arrow" (click)="toggleSort()">
                  {{ arrowIcon$ | async }}
                </mat-icon>
              </div>
              <div content>
                <ng-container *ngIf="businessesLoading$ | async; then loading; else businessList"></ng-container>
                <ng-container *ngIf="businessesLoadingMore$ | async; then loading"></ng-container>

                <glxy-infinite-scroll-trigger
                  (isVisible)="loadMore()"
                  [visiblilityMargin]="500"
                ></glxy-infinite-scroll-trigger>
              </div>
            </app-manage-accounts-filters>
          </div>
        </ng-container>
        <ng-container drawer-content *ngIf="sidePanelState$ | async as sidePanelState">
          <div [ngSwitch]="sidePanelState.state">
            <app-campaign-side-panel
              *ngSwitchCase="SidePanelState.campaignCreate"
              [accountGroupID]="sidePanelState.data.accountGroupId"
            ></app-campaign-side-panel>
          </div>
        </ng-container>
      </sales-ui-slide-out-panel>
    </ng-container>
  </app-side-drawer-container>
</glxy-page>

<ng-template #businessList>
  <ng-container *ngIf="agidQueryParam$ | async as accountGroupId">
    <mat-card appearance="outlined">
      <app-business-loading-banner [accountGroupId]="accountGroupId"></app-business-loading-banner>
    </mat-card>
  </ng-container>
  <ng-container *ngIf="showSuccess$ | async">
    <ng-container *ngIf="businesses$ | async as businesses">
      <ng-container *ngIf="businesses.length > 0; else empty">
        <div data-cy="account-cards" *ngFor="let business of businesses">
          <app-account-card
            [business]="business"
            [zoomEnabled]="zoomFeatureEnabled$ | async"
            [meetEnabled]="meetFeatureEnabled$ | async"
            (snapshotRefreshClickedEvent)="openSnapshotRefreshModal($event)"
            (openComposerClickedEvent)="openComposerModal($event)"
            (snapshotCreateClickedEvent)="snapshotCreateClickEvent($event)"
            (campaignClick)="campaignSendMail($event)"
          ></app-account-card>
        </div>
      </ng-container>
    </ng-container>
  </ng-container>
  <ng-container *ngIf="showError$ | async">
    <uikit-empty-state
      iconName="error_outline"
      title="{{ 'ERRORS.UNABLE_TO_LOAD_RESULTS' | translate }}"
    ></uikit-empty-state>
  </ng-container>
</ng-template>

<ng-template #loading>
  <div class="accounts-container">
    <uikit-list-stencil [showHeader]="false" rowHeight="184px" [numRows]="3"></uikit-list-stencil>
  </div>
</ng-template>
<ng-template #pageLoader>
  <div class="accounts-container">
    <uikit-list-stencil
      class="accounts-container"
      [showHeader]="true"
      rowHeight="184px"
      [numRows]="3"
    ></uikit-list-stencil>
  </div>
</ng-template>

<ng-template #empty>
  <ng-container *ngIf="lastChangedTerm$ | async as termChanged" [ngSwitch]="termChanged">
    <ng-container *ngSwitchCase="FILTER_FIELDS.SALESPERSON">
      <glxy-empty-state class="empty-states">
        <glxy-empty-state-hero>
          <uikit-v-img src="assets/accounts-empty-states/empty-accounts-create.svg"></uikit-v-img>
        </glxy-empty-state-hero>
        <glxy-empty-state-title>
          {{ 'MANAGE_ACCOUNTS.EMPTY_STATES.CREATE_ACCOUNTS_TITLE' | translate }}
        </glxy-empty-state-title>
        <p>
          {{ 'MANAGE_ACCOUNTS.EMPTY_STATES.CREATE_ACCOUNTS_INFO' | translate }}
        </p>
        <p>
          {{ 'MANAGE_ACCOUNTS.EMPTY_STATES.CREATE_ACCOUNTS_SUGGESTIONS' | translate }}
        </p>
      </glxy-empty-state>
    </ng-container>
    <ng-container *ngSwitchCase="FILTER_FIELDS.FILTERS">
      <glxy-empty-state class="empty-states">
        <glxy-empty-state-hero>
          <uikit-v-img src="assets/accounts-empty-states/empty-accounts-question.svg"></uikit-v-img>
        </glxy-empty-state-hero>
        <glxy-empty-state-title>
          {{ 'MANAGE_ACCOUNTS.EMPTY_STATES.FILTER_ACCOUNTS_TITLE' | translate }}
        </glxy-empty-state-title>
        <p>
          {{ 'MANAGE_ACCOUNTS.EMPTY_STATES.FILTER_ACCOUNTS_INFO' | translate }}
        </p>
      </glxy-empty-state>
    </ng-container>
    <ng-container *ngSwitchCase="FILTER_FIELDS.SEARCH">
      <glxy-empty-state class="empty-states">
        <glxy-empty-state-hero>
          <uikit-v-img src="assets/accounts-empty-states/empty-accounts-question.svg"></uikit-v-img>
        </glxy-empty-state-hero>
        <glxy-empty-state-title>
          {{ 'MANAGE_ACCOUNTS.EMPTY_STATES.SEARCH_ACCOUNTS_TITLE' | translate }}
        </glxy-empty-state-title>
        <p>
          {{ 'MANAGE_ACCOUNTS.EMPTY_STATES.SEARCH_ACCOUNTS_INFO' | translate }}
        </p>
        <p>
          {{ 'MANAGE_ACCOUNTS.EMPTY_STATES.SEARCH_ACCOUNTS_SUGGESTIONS' | translate }}
        </p>
        <p>
          <button mat-raised-button id="empty-state-email" color="primary" (click)="openEmailSearchPage()">
            {{ 'MANAGE_ACCOUNTS.EMPTY_STATES.SEARCH_ACCOUNTS_CONTACT_SUGGESTION' | translate }}
          </button>
        </p>
      </glxy-empty-state>
    </ng-container>
  </ng-container>
</ng-template>
