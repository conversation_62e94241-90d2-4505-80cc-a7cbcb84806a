import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { Environment, EnvironmentService } from '@galaxy/core';
import { Observable } from 'rxjs';
import { filter, map, scan, shareReplay, switchMap } from 'rxjs/operators';
import { ApiServiceInterface } from '../interfaces/api-service-interface';

export class LoggedInUserInfo {
  constructor(
    public salespersonId: string,
    public isImpersonation: boolean,
    public hasAccessToAllAccountsInMarket: boolean,
    public partnerId: string,
    public marketId: string,
    public phoneExtension: string,
    public unifiedUserId: string,
    public isSalesManager: boolean,
    public isSalesperson: boolean,
    public partnerCreated: string,
    public userCreated: string,

    // The number of seconds since the partner or user was created.
    public partnerCreatedAt: number,
    public userCreatedAt: number,
  ) {}
}

interface LoggedInUserInfoResponseData {
  salespersonId: string;
  isImpersonation: boolean;
  has_access_to_all_accounts_in_market: boolean;
  partnerId: string;
  marketId: string;
  phoneExtension: string;
  unifiedUserId: string;
  isSalesManager: boolean;
  isSalesperson: boolean;

  // For some reason either partnerCreated and userCreated come back OR partnerCreatedAt and createdAt
  partnerCreated?: string;
  userCreated?: string;

  partnerCreatedAt?: number;
  createdAt?: number;
}

export interface LoggedInUserInfoResponse {
  data: LoggedInUserInfoResponseData;
}

export class PartnerMarket {
  constructor(public readonly partnerId: string, public readonly marketId: string) {}

  hasMarket(): boolean {
    return Boolean(this.marketId);
  }
}

@Injectable({
  providedIn: 'root',
})
export class LoggedInUserInfoService {
  constructor(
    private readonly http: HttpClient,
    @Inject('ApiServiceInterface') private readonly api: ApiServiceInterface,
    private readonly env: EnvironmentService,
  ) {}
  LOGGED_IN_USER_INFO_URL = '/internalApi/v2/logged-in-user-info/';

  public readonly loggedInUserInfo$: Observable<LoggedInUserInfo> = this.http
    .get<LoggedInUserInfoResponse>(this.LOGGED_IN_USER_INFO_URL, { withCredentials: true })
    .pipe(
      map((res: LoggedInUserInfoResponse) => {
        // Multiply createdAt by 1000 because we're getting the units in seconds and we need milliseconds.
        const userCreatedAt = res.data.createdAt ? res.data.createdAt * 1000 : 0;
        const partnerCreatedAt = res.data.partnerCreatedAt ? res.data.partnerCreatedAt * 1000 : 0;
        // Passing invalid as last argument so Date objects become invalid and NaN checks pass.
        const partnerCreated = new Date(res.data.partnerCreated || partnerCreatedAt || 'invalid');
        const userCreatedDate = new Date(res.data.userCreated || userCreatedAt || 'invalid');
        const isSalesperson = !!res.data.salespersonId;
        return new LoggedInUserInfo(
          res.data.salespersonId || '',
          res.data.isImpersonation || false,
          res.data.has_access_to_all_accounts_in_market || false,
          res.data.partnerId || '',
          res.data.marketId || '',
          res.data.phoneExtension || '',
          res.data.unifiedUserId || '',
          res.data.isSalesManager || false,
          isSalesperson,
          isNaN(partnerCreated.getTime()) ? '' : partnerCreated.toISOString(),
          isNaN(userCreatedDate.getTime()) ? '' : userCreatedDate.toISOString(),
          partnerCreated.getTime() || 0,
          userCreatedDate.getTime() || 0,
        );
      }),
      // TODO rlaforge: Figure out why LoggedInUser info returns different date properties.
      // Sometimes empty strings are returned for partnerCreated / userCreated.
      // Sometimes we get second values for partnerCreatedAt and userCreatedAt.
      // This scan attempts to ensure the dates stay accurate in the face of different response types.
      scan((previousUser: LoggedInUserInfo | undefined, newUser: LoggedInUserInfo) => {
        if (!previousUser) {
          return newUser;
        }
        newUser.partnerCreated = newUser.partnerCreated || previousUser.partnerCreated;
        newUser.userCreated = newUser.userCreated || previousUser.userCreated;
        newUser.partnerCreatedAt = newUser.partnerCreatedAt || previousUser.partnerCreatedAt;
        newUser.userCreatedAt = newUser.userCreatedAt || previousUser.userCreatedAt;
        return newUser;
      }, undefined),
      filter<LoggedInUserInfo>(Boolean),
      shareReplay(1),
    );

  public readonly vbcHost$: Observable<string> = this.loggedInUserInfo$.pipe(
    switchMap((info) => this.fetchVbcHost(info.partnerId, info.marketId)),
    shareReplay(1),
  );

  readonly partnerId$: Observable<string> = this.loggedInUserInfo$.pipe(map((l) => l.partnerId));

  readonly marketId$: Observable<string> = this.loggedInUserInfo$.pipe(map((l) => l.marketId));

  readonly partnerIdAndMarketId$: Observable<PartnerMarket> = this.loggedInUserInfo$.pipe(
    map((l) => new PartnerMarket(l.partnerId, l.marketId)),
  );

  unifiedUserId$: Observable<string> = this.loggedInUserInfo$.pipe(map((l) => l.unifiedUserId));

  salesPersonId$: Observable<string> = this.loggedInUserInfo$.pipe(map((l) => l.salespersonId));

  phoneExtension$: Observable<string> = this.loggedInUserInfo$.pipe(map((l) => l.phoneExtension));

  hasAccessToAllAccountsInMarket$: Observable<boolean> = this.loggedInUserInfo$.pipe(
    map((l) => l.hasAccessToAllAccountsInMarket),
  );

  isSalesManager$: Observable<boolean> = this.loggedInUserInfo$.pipe(map((l) => l.isSalesManager));

  isSalesperson$: Observable<boolean> = this.loggedInUserInfo$.pipe(map((l) => l.isSalesperson));

  /**
   * @deprecated getPartnerId() is deprecated. use partnerId$
   */
  getPartnerId(): Observable<string> {
    return this.partnerId$;
  }

  /**
   * @deprecated getMarketId() is deprecated. use marketId$
   */
  getMarketId(): Observable<string> {
    return this.marketId$;
  }

  /**
   * @deprecated getPartnerIdAndMarketId() is deprecated. use partnerIdAndMarketId$
   */
  getPartnerIdAndMarketId$(): Observable<PartnerMarket> {
    return this.partnerIdAndMarketId$;
  }

  private fetchVbcHost(partnerId: string, marketId: string): Observable<string> {
    let vbcApiHost = 'https://vbc-demo.appspot.com';
    if (this.env.getEnvironment() === Environment.PROD) {
      vbcApiHost = 'https://vbc-prod.appspot.com';
    }
    const url = `${vbcApiHost}/internalApi/v3/partner/getHostnamesByPartnerId/`;
    return this.api.get(`${url}?partnerId=${partnerId}&marketId=${marketId}`).pipe(
      map((response) => response['partnerHostnames'][response['partnerHostnames'].length - 1]),
      map((hostname) => `https://${hostname}`),
    );
  }
}
