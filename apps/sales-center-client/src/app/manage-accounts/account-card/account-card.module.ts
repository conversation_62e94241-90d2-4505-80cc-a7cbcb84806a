import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterModule } from '@angular/router';
import { ScorecardModule, WhitelabelTranslationModule } from '@galaxy/snapshot';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { GoogleMeetInstantMeetingModule, ZoomInstantMeetingModule } from '@vendasta/integrations';
import { CommonPipesModule } from '@vendasta/uikit';
import { CopyToClipBoardAndAlertModule } from '../../common';
import { ContactFormsModule } from '../../common/contacts';
import { ContactsMenuActionsModule } from '../../common/contacts/contacts-phone-button/contacts-menu-actions.module';
import {
  addContactForInstantMeetingDialog,
  INSTANT_MEETING_ADD_CONTACT_HANDLER,
} from '../../meetings/factories/add-contact-handler/add-contact-handler';
import {
  logSalesActivityForJoinMeeting,
  LOG_JOIN_MEETING_SALES_ACTIVITY_TOKEN,
} from '../../meetings/factories/join-meeting-sales-activity/join-meeting-sales-activity';
import { MeetingEventsModule } from '../../meetings/meeting-events/meeting-events.module';
import { MeetingEventsService } from '../../meetings/meeting-events/meeting-events.service';
import { ProductActivationsModule } from '../../product-activations/product-activations.module';
import { SalesActivityApiService } from '../../sales-activity/sales-activity-api.service';
import { ScreenshareModule } from '../../screenshare';
import { SnapshotScorecardModule } from '../../snapshot-scorecard/snapshot-scorecard.module';
import { AccountCardComponent } from './account-card.component';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { InboxButtonModule } from '@galaxy/conversation/button';
import { InboxModalGuard } from '../../guards/inbox-modal.guard';
import { canAccessInboxModal, Features } from '../../features';
import { FeatureFlagService } from '../../core';

@NgModule({
  declarations: [AccountCardComponent],
  exports: [AccountCardComponent],
  imports: [
    CommonModule,
    TranslateModule,
    RouterModule,
    // MAT MODULES
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatMenuModule,
    MatTooltipModule,
    MatDialogModule,
    // GALAXY MODULES
    GalaxyBadgeModule,
    GalaxyPipesModule,
    GalaxyLoadingSpinnerModule,
    // OTHER MODULES
    GoogleMeetInstantMeetingModule,
    ZoomInstantMeetingModule,
    ScorecardModule,
    SnapshotScorecardModule,
    ContactFormsModule,
    CopyToClipBoardAndAlertModule,
    MeetingEventsModule,
    ContactsMenuActionsModule,
    CommonPipesModule,
    WhitelabelTranslationModule,
    ScreenshareModule,
    ProductActivationsModule,
    InboxButtonModule,
  ],
  providers: [
    MeetingEventsService,
    {
      provide: LOG_JOIN_MEETING_SALES_ACTIVITY_TOKEN,
      useFactory: logSalesActivityForJoinMeeting,
      deps: [MeetingEventsService, SalesActivityApiService, TranslateService],
    },
    {
      provide: INSTANT_MEETING_ADD_CONTACT_HANDLER,
      useFactory: addContactForInstantMeetingDialog,
      deps: [MeetingEventsService, MatDialog],
    },
    InboxModalGuard,
    {
      provide: Features.Inbox,
      useFactory: canAccessInboxModal,
      deps: [FeatureFlagService, 'PARTNER_ID'],
    },
  ],
})
export class AccountCardModule {}
