import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'ssl',
  standalone: false,
})
export class SslPipe implements PipeTransform {
  static process(value: string): string {
    if (!value) {
      return value;
    }
    if (value.startsWith('http://')) {
      return value.replace(new RegExp('^http://'), 'https://');
    }
    return value;
  }

  transform(value: string): string {
    return SslPipe.process(value);
  }
}
