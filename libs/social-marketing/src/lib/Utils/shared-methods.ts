import { DraftType, VisibilityType, SSIDDraftTypeInterface } from '@vendasta/social-drafts';

import { PostType } from '@vendasta/social-posts';

export function draftVisibilityToEnum(visibility: string): VisibilityType {
  switch (visibility) {
    case 'all':
      return VisibilityType.VISIBILITY_TYPE_ALL;
    case 'visible':
      return VisibilityType.VISIBILITY_TYPE_VISIBLE;
    case 'hidden':
      return VisibilityType.VISIBILITY_TYPE_HIDDEN;
    default:
      return VisibilityType.VISIBILITY_TYPE_VISIBLE;
  }
}

export function mapDraftTypes(ssidDraftTypes: SSIDDraftTypeInterface[]): PostType {
  // we are injecting one posType per draft so we have reduce the array to one postType
  if (!ssidDraftTypes?.length) {
    return PostType.POST_TYPE_INVALID;
  }
  const avPostType = ssidDraftTypes.map((ssidDraftType: SSIDDraftTypeInterface) =>
    convertDraftTypeToPostType(ssidDraftType?.draftType || DraftType.DRAFT_TYPE_INVALID),
  );
  return (
    avPostType.find((postType) => postType === PostType.POST_TYPE_STORIES) ||
    avPostType.find((postType) => postType !== PostType.POST_TYPE_INVALID) ||
    PostType.POST_TYPE_INVALID
  );
}

export function convertDraftTypeToPostType(draftType: DraftType): PostType {
  switch (draftType) {
    case DraftType.DRAFT_TYPE_INVALID:
      return PostType.POST_TYPE_INVALID;
    case DraftType.DRAFT_TYPE_IMAGE:
      return PostType.POST_TYPE_IMAGE;
    case DraftType.DRAFT_TYPE_VIDEO:
      return PostType.POST_TYPE_VIDEO;
    case DraftType.DRAFT_TYPE_GIF:
      return PostType.POST_TYPE_GIF;
    case DraftType.DRAFT_TYPE_REEL:
      return PostType.POST_TYPE_REEL;
    case DraftType.DRAFT_TYPE_CAROUSEL:
      return PostType.POST_TYPE_CAROUSEL;
    case DraftType.DRAFT_TYPE_TEXT:
      return PostType.POST_TYPE_TEXT;
    case DraftType.DRAFT_TYPE_STORIES:
      return PostType.POST_TYPE_STORIES;
    default:
      return PostType.POST_TYPE_INVALID;
  }
}

//Since there are two different DraftIds, this function will help check the conditions in other files.
export function isDraft(post: any) {
  return post ? post?.draft_id || post?.draftId : false;
}

export function isBlog(post: any) {
  return post
    ? post?.service === 'WP_BLOG' || post?.blogPostCustomization?.title || post?.postId?.includes('Blog')
    : false;
}
