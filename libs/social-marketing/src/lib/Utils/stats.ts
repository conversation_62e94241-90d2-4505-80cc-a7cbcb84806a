export class LinkedinStats {
  commentCount: number;
  likeCount: number;
  impressionCount: number;
  shareCount: number;
  clickCount: number;

  constructor() {
    this.commentCount = 0;
    this.likeCount = 0;
    this.impressionCount = 0;
    this.shareCount = 0;
    this.clickCount = 0;
  }
}

export class FacebookReaction {
  commentCount: number;
  peopleReached: number;
  likeCount: number;
  shareCount: number;
  totalReaction: number;
  reactions: string[];
  likeUserNames: string[];
  hasLiked: boolean;

  constructor() {
    this.commentCount = 0;
    this.peopleReached = 0;
    this.likeCount = 0;
    this.shareCount = 0;
    this.totalReaction = 0;
    this.reactions = [];
    this.likeUserNames = [];
    this.hasLiked = false;
  }
}

export class TwitterStats {
  favourites: number;
  hasFavourited: boolean;
  retweets: number;
  hasRetweeted: boolean;

  constructor() {
    this.favourites = 0;
    this.hasFavourited = false;
    this.retweets = 0;
    this.hasRetweeted = false;
  }
}

export class GMBStats {
  views: number;
  clicks: number;

  constructor() {
    this.views = 0;
    this.clicks = 0;
  }
}

export class InstagramStats {
  commentsCount: number;
  likeCount: number;
  reach: number;
  saves: number;
  videoViews: number;

  constructor() {
    this.commentsCount = 0;
    this.likeCount = 0;
    this.reach = 0;
    this.saves = 0;
    this.videoViews = 0;
  }
}

export class YoutubeStats {
  commentCount: number;
  likeCount: number;
  viewCount: number;
  favoriteCount: number;

  constructor() {
    this.commentCount = 0;
    this.likeCount = 0;
    this.viewCount = 0;
    this.favoriteCount = 0;
  }
}

export class TikTokStats {
  likeCount: number;
  commentCount: number;
  shareCount: number;
  reachCount: number;

  constructor() {
    this.likeCount = 0;
    this.commentCount = 0;
    this.shareCount = 0;
    this.reachCount = 0;
  }
}
