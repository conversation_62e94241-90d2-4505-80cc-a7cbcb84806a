import { HttpErrorResponse } from '@angular/common/http';
import { TestScheduler } from 'rxjs/testing';
import { ApiServiceMock } from './../interfaces/api-service-interface.mock';
import { LoggedInUserInfoService, LoggedInUserInfoResponse, LoggedInUserInfo } from './logged-in-user-info.service';

let scheduler: TestScheduler;

class MockHttpClient<T> {
  mockGetResponse: T;
  mockError: any;
  constructor(mockGetResponse: T) {
    this.mockGetResponse = mockGetResponse;
  }
  get = jest.fn(() => {
    const noError = scheduler.createColdObservable('--x', { x: this.mockGetResponse });
    const withError = scheduler.createColdObservable('--#', null, this.mockError);
    return this.mockError ? withError : noError;
  });
}

describe('LoggedInUserInfoService', () => {
  let loggedInUserInfoService: LoggedInUserInfoService;
  let apiService: ApiServiceMock;
  let mockHttpClient: MockHttpClient<LoggedInUserInfoResponse>;

  beforeEach(() => {
    scheduler = new TestScheduler((a, b) => expect(a).toEqual(b));
    apiService = new ApiServiceMock(scheduler);
    mockHttpClient = new MockHttpClient<LoggedInUserInfoResponse>({
      data: {
        salespersonId: 'numberOneNoodle',
        isImpersonation: false,
        partnerId: 'ABC',
        marketId: 'scrummi',
        isSalesManager: true,
        isSalesperson: true,
        has_access_to_all_accounts_in_market: undefined,
        phoneExtension: undefined,
        unifiedUserId: undefined,
        partnerCreated: undefined,
        userCreated: undefined,
      },
    });
    loggedInUserInfoService = new LoggedInUserInfoService(mockHttpClient as any, apiService, null);
  });
  afterEach(() => scheduler.flush());

  describe('constructor', () => {
    it('correctly gets user info data from http', () => {
      scheduler.expectObservable(loggedInUserInfoService.loggedInUserInfo$).toBe('--x', {
        x: {
          salespersonId: 'numberOneNoodle',
          isImpersonation: false,
          partnerId: 'ABC',
          marketId: 'scrummi',
          isSalesManager: true,
          isSalesperson: true,
          hasAccessToAllAccountsInMarket: false,
          phoneExtension: '',
          unifiedUserId: '',
          partnerCreated: '',
          userCreated: '',
          partnerCreatedAt: 0,
          userCreatedAt: 0,
        } as LoggedInUserInfo,
      });
    });

    it('correctly handles error', () => {
      const err = new HttpErrorResponse({ status: 403 });
      mockHttpClient.mockError = err;
      loggedInUserInfoService = new LoggedInUserInfoService(mockHttpClient as any, apiService, null);
      scheduler.expectObservable(loggedInUserInfoService.loggedInUserInfo$).toBe('--#', null, err);
    });
  });

  describe('partnerIdAndMarketId$', () => {
    it('should return an object with both partnerId and marketId', () => {
      scheduler.expectObservable(loggedInUserInfoService.partnerIdAndMarketId$).toBe('--x', {
        x: {
          partnerId: 'ABC',
          marketId: 'scrummi',
        },
      });
    });
  });
});
