<div class="audit-results-container" *ngIf="auditData">
  <div class="audit-section">
    <mat-card appearance="outlined" class="audit-card">
      <mat-card-content class="audit-card-content">
        <div class="progress-card-layout">
          <div class="progress-column">
            <div class="circular-gauge-container">
              <div
                class="gauge-wrapper"
                role="progressbar"
                [attr.aria-valuenow]="overallScore"
                aria-valuemin="0"
                aria-valuemax="100"
                [attr.aria-label]="'AIEO.RESULTS.ARIA_LABEL' | translate: { score: overallScore }"
              >
                <svg class="gauge-svg" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
                  <circle
                    class="gauge-background"
                    cx="60"
                    cy="60"
                    r="50"
                    fill="none"
                    stroke="var(--border-color, #e0e0e0)"
                    stroke-width="8"
                  />
                  <circle
                    class="gauge-progress"
                    cx="60"
                    cy="60"
                    r="50"
                    fill="none"
                    stroke="url(#gauge-gradient)"
                    stroke-width="8"
                    stroke-linecap="round"
                    [attr.stroke-dasharray]="getGaugeCircumference()"
                    [attr.stroke-dashoffset]="getGaugeOffset()"
                    transform="rotate(-90 60 60)"
                  />
                  <defs>
                    <linearGradient id="gauge-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                      <stop offset="0%" [attr.stop-color]="getGaugeColor(0)" />
                      <stop offset="50%" [attr.stop-color]="getGaugeColor(50)" />
                      <stop offset="100%" [attr.stop-color]="getGaugeColor(100)" />
                    </linearGradient>
                  </defs>
                </svg>
                <div class="gauge-center">
                  <div class="gauge-score">{{ overallScore }}%</div>
                  <div class="gauge-label">{{ scoreLabel }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="content-column">
            <div class="audit-summary">
              <p class="audit-description">
                {{ auditData?.description || '' }}
              </p>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <div class="audit-section" *ngFor="let section of sections; trackBy: trackBySection">
    <div class="audit-section-header">
      <h3 class="section-title">{{ section?.title || ('AIEO.RESULTS.UNTITLED_SECTION' | translate) }}</h3>
    </div>
    <div class="audit-section-content">
      <div class="progress-indicator">
        <div class="progress-percentage">
          <glxy-badge [color]="getBadgeColor(section?.score || 0)" [size]="'small'">
            {{ section?.score || 0 }}%
          </glxy-badge>
        </div>
        <div class="progress-description">
          <p class="audit-text" *ngIf="isStringContent(section.content)">{{ section.content }}</p>
          <ul class="audit-list" *ngIf="isArrayContent(section.content)">
            <li *ngFor="let item of section.content">{{ item }}</li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <div class="audit-footer">
    <button mat-raised-button color="primary" class="run-another-button" (click)="onRunAnotherAudit()">
      {{ 'AIEO.RESULTS.RUN_ANOTHER_AUDIT' | translate }}
    </button>
  </div>
</div>
