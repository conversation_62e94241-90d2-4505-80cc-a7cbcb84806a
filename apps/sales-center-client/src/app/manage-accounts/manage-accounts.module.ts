import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterModule } from '@angular/router';
import { PartnerService } from '@galaxy/partner';
import { ScorecardModule, SnapshotCheckoutModule, WhitelabelTranslationModule } from '@galaxy/snapshot';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { AuxiliaryDataModule } from '@vendasta/auxiliary-data-components';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { GalaxyInfiniteScrollTriggerModule } from '@vendasta/galaxy/infinite-scroll-trigger';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { GoogleMeetInstantMeetingModule, ZoomInstantMeetingModule } from '@vendasta/integrations';
import { ProductAnalyticsModule } from '@vendasta/product-analytics';
import { HostService, OnboardingStrategiesApiService } from '@vendasta/prospect';
import { SidePanelStateService, SlideOutPanelModule } from '@vendasta/sales-ui';
import { SalespersonServiceModule } from '@vendasta/salesperson';
import {
  EmptyStateModule,
  UIKitModule,
  VImgModule,
  VaBadgeModule,
  VaFilterModule,
  VaIconModule,
  VaStencilsModule,
} from '@vendasta/uikit';
import { VFormModule } from '@vendasta/vform';
import { SubTitleModule } from '../account-details/sub-title/sub-title.module';
import { CampaignsModule } from '../campaigns';
import { CopyToClipBoardAndAlertModule, SalesToolCommonModule } from '../common';
import { BusinessLoadingBannerModule } from '../common/business-loading-banner';
import { BusinessPrioritiesService } from '../common/business-priorities.service';
import { ContactFormsModule } from '../common/contacts';
import { ContactsMenuActionsModule } from '../common/contacts/contacts-phone-button/contacts-menu-actions.module';
import { AddSalesActivityDialogModule } from '../common/sales-activity/add-sales-activity-dialog';
import { DynamicOpenCloseTemplateRefService } from '../common/side-drawer/dynamic-open-close-template-ref.service';
import { SideDrawerModule } from '../common/side-drawer/side-drawer.module';
import { MeetingEventsModule } from '../meetings/meeting-events/meeting-events.module';
import { NavigationModule } from '../navigation/navigation.module';
import { ProductActivationsModule } from '../product-activations/product-activations.module';
import { SalesActivityModule } from '../sales-activity/sales-activity.module';
import { SnapshotScorecardModule } from '../snapshot-scorecard/snapshot-scorecard.module';
import { AccountCardModule } from './account-card/account-card.module';
import { LIFECYCLE_OPTIONS_TOKEN, getLifecycleStageFilterOptions } from './manage-accounts-filters-lifecycle';
import { ManageAccountsFiltersComponent } from './manage-accounts-filters/manage-accounts-filters.component';
import { ManageAccountsFiltersService } from './manage-accounts-filters/manage-accounts-filters.service';
import { ManageAccountsUiService } from './manage-accounts-ui.service';
import { ManageAccountsComponent } from './manage-accounts.component';
import { TeamsOfSalespeopleService } from './teams-of-salespeople.service';

@NgModule({
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatInputModule,
    MatMenuModule,
    MatFormFieldModule,
    MatTooltipModule,
    MatButtonModule,
    MatSelectModule,
    VaStencilsModule,
    SalespersonServiceModule,
    VaFilterModule,
    RouterModule,
    EmptyStateModule,
    NavigationModule,
    VaBadgeModule,
    VaIconModule,
    MatDialogModule,
    ReactiveFormsModule,
    FormsModule,
    MatButtonModule,
    SalesActivityModule,
    SnapshotCheckoutModule,
    VFormModule,
    ProductActivationsModule,
    CampaignsModule,
    TranslateModule,
    UIKitModule,
    BusinessLoadingBannerModule,
    WhitelabelTranslationModule,
    SubTitleModule,
    ContactFormsModule,
    ContactsMenuActionsModule,
    ZoomInstantMeetingModule,
    VImgModule,
    MeetingEventsModule,
    GoogleMeetInstantMeetingModule,
    GalaxyAlertModule,
    GalaxyPageModule,
    SideDrawerModule,
    ScorecardModule,
    SalesToolCommonModule,
    SnapshotScorecardModule,
    GalaxyEmptyStateModule,
    SlideOutPanelModule,
    AddSalesActivityDialogModule,
    GalaxyInfiniteScrollTriggerModule,
    ProductAnalyticsModule,
    MatProgressSpinnerModule,
    CopyToClipBoardAndAlertModule,
    GalaxyPipesModule,
    AuxiliaryDataModule,
    GalaxyBadgeModule,
    MatTabsModule,
    AccountCardModule,
  ],
  declarations: [ManageAccountsComponent, ManageAccountsFiltersComponent],
  providers: [
    TeamsOfSalespeopleService,
    PartnerService,
    BusinessPrioritiesService,
    OnboardingStrategiesApiService,
    HostService,
    ManageAccountsFiltersService,
    ManageAccountsUiService,

    DynamicOpenCloseTemplateRefService,
    SidePanelStateService,
    {
      provide: LIFECYCLE_OPTIONS_TOKEN,
      useFactory: getLifecycleStageFilterOptions,
      deps: [TranslateService],
    },
  ],
  exports: [],
})
export class ManageAccountsModule {}
