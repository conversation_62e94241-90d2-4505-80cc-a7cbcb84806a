import { SalespersonCacheService } from './salesperson-cache.service';
import { AppCacheService, CacheGetResponse, ClientOrigin } from '@vendasta/sales-ui';

describe('Salesperson cache service', () => {
  describe('Getting Salespeople', () => {
    it('Should get a salesperson entry properly', () => {
      const USER_ID = 'UID-salesperson-id';
      const mockObject = { ownerId: USER_ID, salespeople: [{ fullName: 'test-name' }] };
      class AlwaysHitCache {
        get(origin: ClientOrigin): CacheGetResponse {
          return <CacheGetResponse>{
            origin: origin,
            isCacheHit: true,
            value: mockObject,
          };
        }
      }
      const serviceToTest = new SalespersonCacheService(new AlwaysHitCache() as AppCacheService);
      const actualValue = serviceToTest.getCachedSalespeople(USER_ID);
      expect(actualValue.ownerId).toBe(USER_ID);
    });
    it('Should return null on a cache miss', () => {
      const USER_ID = 'UID-salesperson-id';
      class AlwaysMissCache {
        get(origin: ClientOrigin): CacheGetResponse {
          return <CacheGetResponse>{
            origin: origin,
            isCacheHit: false,
            value: { anything: 'anything' },
          };
        }
      }
      const serviceToTest = new SalespersonCacheService(new AlwaysMissCache() as AppCacheService);
      const actualValue = serviceToTest.getCachedSalespeople(USER_ID);
      expect(actualValue).toBeFalsy();
    });
    it('Should return null on a userId mismatch', () => {
      const USER_ID = 'UID-salesperson-id';
      class AlwaysHitCache {
        get(origin: ClientOrigin): CacheGetResponse {
          return <CacheGetResponse>{
            origin: origin,
            isCacheHit: true,
            value: { ownerId: USER_ID, anything: 'anything' },
          };
        }
      }
      const serviceToTest = new SalespersonCacheService(new AlwaysHitCache() as AppCacheService);
      const actualValue = serviceToTest.getCachedSalespeople('bad-id');
      expect(actualValue).toBeFalsy();
    });
  });
});
