@use 'design-tokens' as *;

@media screen and (max-width: $media--tablet-minimum) {
  :host ::ng-deep {
    .toolbar .table-controls-row {
      flex-flow: row wrap;
      align-items: center;
    }

    .toolbar .filter-button {
      order: 1;
      margin: 24px 8px 24px 0;
    }

    .toolbar .table-controls-row .search-and-text {
      order: 2;
    }

    .toolbar .table-controls-row div:nth-child(4) {
      order: 3;
      flex: 1 auto;
      text-align: center;

      mat-form-field {
        padding-right: 16px;
      }
    }
  }
}

:host ::ng-deep {
  .content-container div[content] {
    padding: $spacing-2;
  }
}

.accounts-container {
  margin: 0 auto;
  max-width: 1200px;
}

uikit-empty-state {
  margin: 64px;
}

select {
  color: $blue;
}

.total-results {
  margin: 8px 0;
}

.card-sorter {
  display: flex;
  flex-flow: row nowrap;

  mat-icon {
    margin: auto 0 auto 8px;
    cursor: pointer;
    user-select: none;
  }
}

.accounts-footer-container {
  text-align: center;
}
