import { HttpParams } from '@angular/common/http';
import { AccountGroup } from '@galaxy/account-group';
import { expectBool, schedule } from '@vendasta/rx-utils';
import { Archived, BusinessApi, BusinessSearchResponse, Campaign, SearchOptions, Snapshot } from '@vendasta/sales';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { map, shareReplay } from 'rxjs/operators';
import { TestScheduler } from 'rxjs/testing';
import { LoggedInUserInfo } from '../logged-in-user-info';
import { SalesStatusFilterId, SalespersonActionId } from './manage-accounts-filters';
import { mockSearchBusinessResponse } from './manage-accounts.mock';
import { LoadBusinessEvent, ManageAccountsService } from './manage-accounts.service';

let sched: TestScheduler;

const mockUser$$ = new BehaviorSubject<LoggedInUserInfo>(<LoggedInUserInfo>{
  salespersonId: 'UID-123',
  isImpersonation: false,
  hasAccessToAllAccountsInMarket: true,
  partnerId: 'PARTNER',
  marketId: 'default',
}).pipe(shareReplay(1));

const mockNextCursor = 'WWE=';

const tagFilters = [
  {
    name: 'tag1',
    value: 'tag1',
  },
  {
    name: 'tag2',
    value: 'tag2',
  },
];

export const mockLoadMoreValues = {
  nextCursor: mockNextCursor,
  loadMore: true,
};

class MockErroringAccountGroupService {
  getMulti(): Observable<AccountGroup[]> {
    return throwError('API will error if 0 accountGroupIds passed in');
  }
}

class MockAccountGroupService {
  getMulti(): Observable<AccountGroup[]> {
    return of([]);
  }
}

class MockApiService {
  happy: boolean;

  constructor(happy = true) {
    this.happy = happy;
  }

  lastParams = '';

  get = (baseUrl: string, params: HttpParams) => {
    this.lastParams = params.toString();
    if (this.happy) {
      return sched.createColdObservable('-x', { x: mockSearchBusinessResponse });
    } else {
      return sched.createColdObservable('-#', {}, new Error('an error happened 😞'));
    }
  };
}

const teamIdWithMembers = 'G-123';
const teamIdWithoutMembers = 'G-321';

class MockTeamsOfSalespeopleService {
  private membersMap = new Map<string, string[]>([
    [teamIdWithMembers, ['SP-123', 'SP-321']],
    [teamIdWithoutMembers, []],
  ]);

  getMembers(groupId: string): Observable<string[]> {
    const members = this.membersMap.get(groupId) || null;
    return of(members);
  }
}

class MockEmptySearchResultsBusinessService implements BusinessApi {
  search(): Observable<BusinessSearchResponse> {
    const emptyResult: BusinessSearchResponse = {
      pagingMetadata: {
        nextCursor: '',
        hasMore: false,
        totalResults: 0,
      },
      businesses: [],
    };
    return of(emptyResult);
  }

  requestSubscriptionChange(): Observable<boolean> {
    return throwError('Not implemented for testing purposes');
  }
}

class MockBusinessService implements BusinessApi {
  lastSortKey: string;
  lastSortAscending: boolean;
  lastSearchTerm?: string;
  lastSalesStatuses?: string[];
  lastSalesAction?: string;
  lastFromDate?: Date;
  lastToDate?: Date;
  lastArchived?: number;
  lastAccountTags?: string[];
  lastBusinessCategories?: string[];
  lastOnCampaign?: number;
  lastSnapshotSent?: number;
  lastAssignees?: string[];

  search(
    partnerId: string,
    marketId: string,
    sortKey: string,
    sortAscending: boolean,
    searchOptions: SearchOptions,
  ): Observable<BusinessSearchResponse> {
    this.lastSortKey = sortKey;
    this.lastSortAscending = sortAscending;
    this.lastSearchTerm = searchOptions.searchTerm;
    this.lastSalesStatuses = searchOptions.salesStatuses;
    this.lastSalesAction = searchOptions.salesAction;
    this.lastFromDate = searchOptions.fromDate;
    this.lastToDate = searchOptions.toDate;
    this.lastArchived = searchOptions.archived;
    this.lastAccountTags = searchOptions.accountTags;
    this.lastBusinessCategories = searchOptions.businessCategories;
    this.lastOnCampaign = searchOptions.onCampaign;
    this.lastSnapshotSent = searchOptions.snapshotSent;
    this.lastAssignees = searchOptions.assignees;

    return of(mockSearchBusinessResponse);
  }

  requestSubscriptionChange(): Observable<boolean> {
    return undefined;
  }
}

describe('BetterManageAccountsService', () => {
  let service: ManageAccountsService;
  let mockApiService: MockApiService;
  let mockBusinessService: MockBusinessService;
  let mockTeamsOfSalespeopleService: MockTeamsOfSalespeopleService;
  let mockAccountGroupService: MockAccountGroupService;

  const arbitraryLoadEvent: LoadBusinessEvent = { salesPeopleIds: ['SP-123'] };

  beforeEach(() => {
    sched = new TestScheduler((a, b) => expect(a).toEqual(b));
  });
  afterEach(() => {
    sched.flush();
  });

  describe('on initialization', () => {
    beforeEach(() => {
      mockApiService = new MockApiService();
      mockBusinessService = new MockBusinessService();
      mockTeamsOfSalespeopleService = new MockTeamsOfSalespeopleService();
      mockAccountGroupService = new MockAccountGroupService();
      service = new ManageAccountsService(
        mockUser$$,
        mockApiService as any,
        mockBusinessService,
        mockTeamsOfSalespeopleService as any,
        null,
        mockAccountGroupService as any,
      );
    });

    it('should set the salesperson as the logged in user', () => {
      const salesPersonId$ = service.salesPerson$.pipe(map((info) => info.salespersonId));
      sched.expectObservable(salesPersonId$).toBe('x', { x: 'UID-123' });
    });
  });

  describe('businessResults$', () => {
    it('should return an empty list if the account group service errors', () => {
      mockApiService = new MockApiService();
      mockTeamsOfSalespeopleService = new MockTeamsOfSalespeopleService();
      mockAccountGroupService = new MockErroringAccountGroupService();
      mockBusinessService = new MockBusinessService();
      service = new ManageAccountsService(
        mockUser$$,
        mockApiService as any,
        mockBusinessService,
        mockTeamsOfSalespeopleService as any,
        null,
        mockAccountGroupService as any,
      );
      sched.schedule(() => service.initLoadBusinesses(arbitraryLoadEvent), sched.createTime('-|'));
      const actualResults$ = service.businessResult$;

      sched.expectObservable(actualResults$).toBe('-x', {
        x: [],
      });
    });

    it('should return an empty list if the business search returns zero results', () => {
      mockApiService = new MockApiService();
      mockTeamsOfSalespeopleService = new MockTeamsOfSalespeopleService();
      mockAccountGroupService = new MockAccountGroupService();
      const mockEmptyResultBusinessSearchService = new MockEmptySearchResultsBusinessService();
      service = new ManageAccountsService(
        mockUser$$,
        mockApiService as any,
        mockEmptyResultBusinessSearchService,
        mockTeamsOfSalespeopleService as any,
        null,
        mockAccountGroupService as any,
      );

      sched.schedule(() => service.initLoadBusinesses(arbitraryLoadEvent), sched.createTime('-|'));
      const actualResults$ = service.businessResult$;

      sched.expectObservable(actualResults$).toBe('-x', {
        x: [],
      });
    });
  });

  describe('loadBusinesses', () => {
    beforeEach(() => {
      mockApiService = new MockApiService();
      mockBusinessService = new MockBusinessService();
      mockTeamsOfSalespeopleService = new MockTeamsOfSalespeopleService();
      mockAccountGroupService = new MockAccountGroupService();
      service = new ManageAccountsService(
        mockUser$$,
        mockApiService as any,
        mockBusinessService,
        mockTeamsOfSalespeopleService as any,
        null,
        mockAccountGroupService as any,
      );
    });

    it('should include all salespeople in request if multiple salespeople on event', () => {
      const id1 = 'SP-123';
      const id2 = 'SP-456';
      const loadEvent: LoadBusinessEvent = { salesPeopleIds: [id1, id2] };
      sched.schedule(() => service.initLoadBusinesses(loadEvent), sched.createTime('-|'));
      sched.flush();
      expect(mockBusinessService.lastAssignees).toEqual([id1, id2]);
    });

    test('assignees should be null if no salespeople on event', () => {
      const loadEvent: LoadBusinessEvent = { salesPeopleIds: [] };
      sched.schedule(() => service.initLoadBusinesses(loadEvent), sched.createTime('-|'));
      sched.flush();
      expect(mockBusinessService.lastAssignees).toBeNull();
    });

    it('should include multiple query parameters in request if multiple events come in', () => {
      const salesPersonId = 'SP-123';
      const salesPersonLoadEvent: LoadBusinessEvent = { salesPeopleIds: [salesPersonId] };
      const createdBeforeDate = new Date();
      const createdBeforeLoadEvent: LoadBusinessEvent = { createdBeforeDate: createdBeforeDate };
      sched.schedule(() => service.initLoadBusinesses(salesPersonLoadEvent), sched.createTime('-|'));
      sched.schedule(() => service.initLoadBusinesses(createdBeforeLoadEvent), sched.createTime('---|'));
      sched.flush();
      expect(mockBusinessService.lastAssignees).toEqual([salesPersonId]);
      expect(mockBusinessService.lastToDate).toEqual(createdBeforeDate);
    });

    it('should include sales activity ids in request if on event', () => {
      const loadEvent: LoadBusinessEvent = {
        activity: {
          activityId: SalesStatusFilterId.ACCOUNT_CREATED,
          value: true,
        },
      };
      sched.schedule(() => service.initLoadBusinesses(loadEvent), sched.createTime('-|'));
      sched.flush();
      expect(mockBusinessService.lastSalesStatuses).toEqual([SalesStatusFilterId.ACCOUNT_CREATED]);
    });

    it('should include tagFilter in request if on event', () => {
      const loadEvent: LoadBusinessEvent = { accountTags: [tagFilters[0]] };
      sched.schedule(() => service.initLoadBusinesses(loadEvent), sched.createTime('-|'));
      sched.flush();
      expect(mockBusinessService.lastAccountTags).toEqual([tagFilters[0].value]);
    });

    it('should contain multiple tagFilters in reques if event detect multiple account Tags', () => {
      const loadEvent: LoadBusinessEvent = { accountTags: tagFilters };
      sched.schedule(() => service.initLoadBusinesses(loadEvent), sched.createTime('-|'));
      sched.flush();
      expect(mockBusinessService.lastAccountTags).toEqual([tagFilters[0].value, tagFilters[1].value]);
    });

    it('should remove business activities if specified by following events', () => {
      const checkedLoadEvent: LoadBusinessEvent = {
        activity: {
          activityId: SalesStatusFilterId.ACCOUNT_CREATED,
          value: true,
        },
      };
      const unCheckedLoadEvent: LoadBusinessEvent = {
        activity: {
          activityId: SalesStatusFilterId.ACCOUNT_CREATED,
          value: false,
        },
      };
      sched.schedule(() => service.initLoadBusinesses(checkedLoadEvent), sched.createTime('-|'));
      sched.schedule(() => service.initLoadBusinesses(unCheckedLoadEvent), sched.createTime('---|'));
      sched.flush();
      expect(mockBusinessService.lastSalesStatuses).toEqual(null);
    });

    it('should include opportunity filters in request if specified by event', () => {
      const radioFieldClickedEvent: LoadBusinessEvent = {
        salespersonActivity: SalespersonActionId.OPPORTUNITY_CREATED,
      };
      sched.schedule(() => service.initLoadBusinesses(radioFieldClickedEvent), sched.createTime('-|'));
      sched.flush();
      expect(mockBusinessService.lastSalesAction).toEqual(SalespersonActionId.OPPORTUNITY_CREATED);
    });

    it('should include createdBeforeDate on request if specified on event', () => {
      const now = new Date();
      const endDateChangedEvent: LoadBusinessEvent = { createdBeforeDate: now };
      sched.schedule(() => service.initLoadBusinesses(endDateChangedEvent), sched.createTime('-|'));
      sched.flush();
      expect(mockBusinessService.lastToDate).toEqual(now);
    });

    it('should include createdAfterDate on request if specified on event', () => {
      const now = new Date();
      const endDateChangedEvent: LoadBusinessEvent = { createdAfterDate: now };
      sched.schedule(() => service.initLoadBusinesses(endDateChangedEvent), sched.createTime('-|'));
      sched.flush();
      expect(mockBusinessService.lastFromDate).toEqual(now);
    });

    it('should include archived on request if specified on event', () => {
      const endDateChangedEvent: LoadBusinessEvent = { archiveStatus: Archived.ARCHIVED_YES };
      sched.schedule(() => service.initLoadBusinesses(endDateChangedEvent), sched.createTime('-|'));
      sched.flush();
      expect(mockBusinessService.lastArchived).toEqual(Archived.ARCHIVED_YES);
    });

    it('should include taxonomy ids in request if specified by event', () => {
      const businessCategoryEvent: LoadBusinessEvent = {
        businessCategories: [{ id: 'active:amateursportsteams', name: 'Amateur Sports Teams' }],
      };

      sched.schedule(() => service.initLoadBusinesses(businessCategoryEvent), sched.createTime('-|'));
      sched.flush();
      expect(mockBusinessService.lastBusinessCategories).toEqual(['active:amateursportsteams']);
    });

    it('should include campaign status on request if specified by event', () => {
      const campaignStatusChangeEvent: LoadBusinessEvent = { campaignStatus: Campaign.CAMPAIGN_ON };
      sched.schedule(() => service.initLoadBusinesses(campaignStatusChangeEvent), sched.createTime('-|'));
      sched.flush();
      expect(mockBusinessService.lastOnCampaign).toEqual(Campaign.CAMPAIGN_ON);
    });

    it('should include snapshot status in request if specified by event', () => {
      const snapshotStatusChangeEvent: LoadBusinessEvent = { snapshotStatus: Snapshot.SNAPSHOT_SENT };
      sched.schedule(() => service.initLoadBusinesses(snapshotStatusChangeEvent), sched.createTime('-|'));
      sched.flush();
      expect(mockBusinessService.lastSnapshotSent).toEqual(Snapshot.SNAPSHOT_SENT);
    });

    it('should include search term in request if specified by event', () => {
      const searchTermChangeEvent: LoadBusinessEvent = { searchTerm: `My Business` };
      schedule(sched, '-|', () => service.initLoadBusinesses(searchTermChangeEvent));
      sched.flush();
      expect(mockBusinessService.lastSearchTerm).toEqual('My Business');
    });

    it('should not include search term in request if not specified by event', () => {
      const searchTermChangeEvent: LoadBusinessEvent = {};
      schedule(sched, '-|', () => service.initLoadBusinesses(searchTermChangeEvent));
      sched.flush();
      expect(mockBusinessService.lastSearchTerm).toEqual(null);
    });

    it('should not include search term in request if EMPTY STRING specified by event', () => {
      const searchTermChangeEvent: LoadBusinessEvent = { searchTerm: '' };
      schedule(sched, '-|', () => service.initLoadBusinesses(searchTermChangeEvent));
      sched.flush();
      expect(mockBusinessService.lastSearchTerm).toEqual(null);
    });

    it('should not clear the search term from request if event without term is provided', () => {
      const eventWithTerm: LoadBusinessEvent = { searchTerm: 'hello' };
      schedule(sched, '-|', () => service.initLoadBusinesses(eventWithTerm));
      const eventWithoutTerm: LoadBusinessEvent = {};
      schedule(sched, '--|', () => service.initLoadBusinesses(eventWithoutTerm));
      sched.flush();
      expect(mockBusinessService.lastSearchTerm).toEqual('hello');
    });

    it('should include sort value in requestUrl if specified by event', () => {
      const searchTermChangeEvent: LoadBusinessEvent = { sortValue: 'name' };
      schedule(sched, '-|', () => service.initLoadBusinesses(searchTermChangeEvent));
      sched.flush();
      expect(mockBusinessService.lastSortKey).toEqual('name');
    });

    it('should sort by hotness in requestUrl if there is no sortValue in the event', () => {
      const searchTermChangeEvent: LoadBusinessEvent = {}; // no sortValue
      schedule(sched, '-|', () => service.initLoadBusinesses(searchTermChangeEvent));
      sched.flush();
      expect(mockBusinessService.lastSortKey).toEqual('hotness');
    });

    it('should sort descending in requestUrl if there is no sortKey in the event', () => {
      const searchTermChangeEvent: LoadBusinessEvent = {}; // no sortValue
      schedule(sched, '-|', () => service.initLoadBusinesses(searchTermChangeEvent));
      sched.flush();
      expect(mockBusinessService.lastSortAscending).toEqual(false);
    });

    it('should sort ascending in requestUrl if event has ascending true', () => {
      const searchTermChangeEvent: LoadBusinessEvent = { sortAscending: true };
      schedule(sched, '-|', () => service.initLoadBusinesses(searchTermChangeEvent));
      sched.flush();
      expect(mockBusinessService.lastSortAscending).toEqual(true);
    });

    it('should sort descending in requestUrl if event has ascending false', () => {
      const searchTermChangeEvent: LoadBusinessEvent = { sortAscending: false };
      schedule(sched, '-|', () => service.initLoadBusinesses(searchTermChangeEvent));
      sched.flush();
      expect(mockBusinessService.lastSortAscending).toEqual(false);
    });

    it('should convert the snake_case response to camelCase', () => {
      sched.schedule(() => service.initLoadBusinesses(arbitraryLoadEvent), sched.createTime('-|'));
      const phoneNumbers$ = service.businessState$.workResults$.pipe(
        map((res) => res.businesses.map((business) => business.phoneNumber)),
      );
      const salesPersonIds$ = service.businessState$.workResults$.pipe(
        map((res) => res.businesses.map((business) => business.salespersonId)),
      );

      sched.expectObservable(phoneNumbers$).toBe('-x', {
        x: ['(*************', '(*************'],
      });
      sched.expectObservable(salesPersonIds$).toBe('-x', {
        x: ['UID-123', 'UID-456'],
      });
    });

    it('should convert the string dates in response to es6 dates', () => {
      sched.schedule(() => service.initLoadBusinesses(arbitraryLoadEvent), sched.createTime('-|'));
      const lastSalesActivityDates$ = service.businessResult$.pipe(
        map((result) => result.map((business) => business.lastSalesActivityDate)),
      );
      sched.expectObservable(lastSalesActivityDates$).toBe('-x', {
        x: [new Date('2018-10-12T14:46:26Z'), new Date('2018-10-11T17:07:27Z')],
      });
    });

    it('should set the number of total results', () => {
      sched.schedule(() => service.initLoadBusinesses(arbitraryLoadEvent), sched.createTime('-|'));
      sched.expectObservable(service.totalResults$).toBe('-x', { x: 2 });
    });

    describe('searchingWithEmptyTeam$', () => {
      const loadEventWithoutTeam: LoadBusinessEvent = { salesPeopleIds: ['SP-123'] };
      const loadEventWithTeamWithNoMembers: LoadBusinessEvent = {
        salesPeopleIds: ['SP-123'],
        salesTeamId: teamIdWithoutMembers,
      };
      const loadEventWithTeamWithMembers: LoadBusinessEvent = {
        salesPeopleIds: ['SP-123'],
        salesTeamId: teamIdWithMembers,
      };

      it('should emit false if not searching by teams', () => {
        sched.schedule(() => service.initLoadBusinesses(loadEventWithoutTeam), sched.createTime('-|'));
        expectBool(sched, service.searchingWithEmptyTeam$).toBe('-f');
      });

      it('should emit true when filtering by a team with no members', () => {
        sched.schedule(() => service.initLoadBusinesses(loadEventWithTeamWithNoMembers), sched.createTime('-|'));
        expectBool(sched, service.searchingWithEmptyTeam$).toBe('-t');
      });

      it('should emit false when filtering by a team with members', () => {
        sched.schedule(() => service.initLoadBusinesses(loadEventWithTeamWithMembers), sched.createTime('-|'));
        expectBool(sched, service.searchingWithEmptyTeam$).toBe('-f');
      });

      it('should emit multiple values for multiple filter events', () => {
        sched.schedule(() => service.initLoadBusinesses(loadEventWithoutTeam), sched.createTime('-|'));
        sched.schedule(() => service.initLoadBusinesses(loadEventWithTeamWithNoMembers), sched.createTime('--|'));
        sched.schedule(() => service.initLoadBusinesses(loadEventWithTeamWithMembers), sched.createTime('---|'));
        sched.schedule(() => service.initLoadBusinesses(loadEventWithoutTeam), sched.createTime('----|'));

        expectBool(sched, service.searchingWithEmptyTeam$).toBe('-ftff');
      });
    });
  });
});
