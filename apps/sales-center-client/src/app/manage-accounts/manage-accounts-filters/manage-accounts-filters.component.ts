import { AfterViewInit, ChangeDetectorRef, Component, Inject, Input, OnDestroy } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { SubscriptionList } from '@vendasta/rx-utils';
import { LifecycleStage } from '@vendasta/sales';
import { FilterField, FilterSection, FilterService, Filters } from '@vendasta/uikit';
import { Observable, combineLatest, firstValueFrom } from 'rxjs';
import { filter, map, switchMap } from 'rxjs/operators';
import { SSCAccessService } from '../../access';
import { AppPage } from '../../access/page-access/app-page.enum';
import { ACCESS_ALL_MARKET_TOKEN } from '../../common/providers';
import { SALES_TEAMS_TOKEN } from '../../sales-teams';
import { ManageAccountFilterId, ManageAccountsFilters, SalesStatusFilterId } from '../manage-accounts-filters';
import { TAXONOMY_OPTIONS } from '../manage-accounts-filters-taxonomy';
import { LoadBusinessEvent } from '../manage-accounts.service';
import { FilterOptions, FilterSelection, ManageAccountsFiltersService } from './manage-accounts-filters.service';

export function parseEvents(events: FilterSelection[]): LoadBusinessEvent {
  const businessEvent: LoadBusinessEvent = {};
  if (events) {
    events.forEach((event) => {
      const eventNewValue = parseEvent(event);
      Object.assign(businessEvent, eventNewValue);
    });
  }
  return businessEvent;
}

function parseEvent($event: FilterSelection): LoadBusinessEvent {
  if (Object.values(SalesStatusFilterId).includes($event.key as SalesStatusFilterId)) {
    const id = $event.key as SalesStatusFilterId;
    return { activity: { activityId: id, value: $event.value } };
  }
  switch ($event.key) {
    case ManageAccountFilterId.SALESPERSON: {
      const ids = $event.value.map((sp) => sp.id);
      return { salesPeopleIds: ids };
    }
    case ManageAccountFilterId.SALESTEAM: {
      const groupId = $event.value !== null ? $event.value.groupId : null;
      return { salesTeamId: groupId };
    }
    case ManageAccountFilterId.ACTION: {
      return { salespersonActivity: $event.value !== null ? $event.value.id : null };
    }
    case ManageAccountFilterId.CREATED_END_DATE:
    case ManageAccountFilterId.CREATED_START_DATE: {
      if ($event.key === ManageAccountFilterId.CREATED_END_DATE) {
        return { createdBeforeDate: $event.value };
      }
      return { createdAfterDate: $event.value };
    }
    case ManageAccountFilterId.ARCHIVE_STATUS: {
      return { archiveStatus: $event.value !== null ? $event.value.id : null };
    }
    case ManageAccountFilterId.BUSINESS_CATEGORY: {
      return { businessCategories: $event.value };
    }
    case ManageAccountFilterId.ON_CAMPAIGN: {
      return { campaignStatus: $event.value !== null ? $event.value.id : null };
    }
    case ManageAccountFilterId.SNAPSHOT_SENT: {
      return { snapshotStatus: $event.value !== null ? $event.value.id : null };
    }
    case ManageAccountFilterId.ACCOUNT_TAGS: {
      return { accountTags: $event.value };
    }
    case ManageAccountFilterId.GOALS: {
      return { goals: $event.value };
    }
    case ManageAccountFilterId.TRAINING_PRIORITIES: {
      return { trainingPriorities: $event.value };
    }
    case ManageAccountFilterId.LIFECYCLE_STAGE: {
      return { lifecycleStage: $event?.value?.id || LifecycleStage.LIFECYCLE_STAGE_UNSET };
    }
    default:
      return {};
  }
}

@Component({
  selector: 'app-manage-accounts-filters',
  templateUrl: './manage-accounts-filters.component.html',
  styleUrls: ['./manage-accounts-filters.component.scss'],
  standalone: false,
})
export class ManageAccountsFiltersComponent implements OnDestroy, AfterViewInit {
  @Input() set toolbarText$(toolbarText: Observable<string>) {
    this.toolbarTexts$ = toolbarText;
  }

  filterSections = new ManageAccountsFilters((key: string) => this.i18n.instant(key));
  filterStructure$: Observable<Filters>;
  toolbarTexts$: Observable<string>;
  subscriptions = SubscriptionList.new();
  readonly savedFilters$ = this.filterService.savedFilters;
  private readonly accessProspects$: Observable<boolean>;

  constructor(
    private readonly i18n: TranslateService,
    private readonly filterService: FilterService,
    private readonly changeDetector: ChangeDetectorRef,
    private readonly accessService: SSCAccessService,
    readonly manageAccountsFiltersService: ManageAccountsFiltersService,
    @Inject(SALES_TEAMS_TOKEN) private readonly accessTeams$: Observable<boolean>,
    @Inject('ACCESS_PIPELINE') private readonly accessPipeline$: Observable<boolean>,
    @Inject(ACCESS_ALL_MARKET_TOKEN) private readonly hasMarketWideAccess$: Observable<boolean>,
  ) {
    this.accessProspects$ = this.accessService.hasAccessToPage(AppPage.PartnerProspectTablePage);
    const title$ = this.i18n.stream('MANAGE_ACCOUNTS.ACCOUNTS_FILTERS_TITLE');
    this.filterStructure$ = combineLatest([
      this.hasMarketWideAccess$,
      this.accessTeams$,
      this.accessPipeline$,
      this.accessProspects$,
      title$,
    ]).pipe(
      map(([hasMarketWideAccess, hasAccessToTeams, canAccessPipeline, canAccessPartnerLeads, title]) => {
        const filters = this.buildFiltersLayout(
          hasMarketWideAccess,
          hasAccessToTeams,
          canAccessPipeline,
          canAccessPartnerLeads,
          title,
        );
        this.filterService.setFilters(filters);
        return filters;
      }),
    );
    this.manageAccountsFiltersService.filterOptions$.subscribe((options) => {
      this.setFilterOptions(options);
    });
    this.subscriptions.add(
      this.manageAccountsFiltersService.filterOptions$.pipe(
        switchMap(() => this.manageAccountsFiltersService.filters$),
        filter((filters) => Boolean(filters)),
      ),
      (filters) => {
        filters.forEach((filterSelection: FilterSelection) => {
          if (
            this.filterService.filters &&
            this.filterService.filters.fields.find((field) => field.name === filterSelection.filterFieldName)
          ) {
            this.filterService.filters.setValueForFieldname(filterSelection.value, filterSelection.filterFieldName);
          }
        });
      },
    );
    this.initHiddenFilters();
  }

  private pushEventForHiddenFilter(changedFilter: FilterField<any>): void {
    switch (changedFilter.id) {
      case ManageAccountFilterId.LIFECYCLE_STAGE:
        this.filterChangedEvent(changedFilter);
    }
  }

  private initHiddenFilters(): void {
    this.subscriptions.add(this.filterService.fieldValueChanges, (changes) => {
      changes.forEach((change) => this.pushEventForHiddenFilter(change));
    });
  }

  private setFilterOptions(options: FilterOptions): void {
    this.filterSections.salesTeamFilter.setOptions(options.salesTeams);
    this.filterSections.salesPersonFilterSection.setOptions(options.salesPeople);
    this.filterSections.businessCategoryFilter.setOptions(TAXONOMY_OPTIONS);
    this.filterSections.accountTagsFilter.setOptions(options.accountTags);
    this.filterSections.goalFilter.setOptions(options.goals);
    this.filterSections.lifecycleFilter.setOptions(options.lifecycleStages);
  }

  private buildFiltersLayout(
    hasMarketWideAccess: boolean,
    hasAccessToTeams: boolean,
    canAccessPipeline: boolean,
    canAccessPartnerLeads: boolean,
    title: string,
  ): Filters {
    if (!hasMarketWideAccess) {
      this.filterSections.salesPersonFilterSection.hideSection = true;
      this.filterSections.salesTeamFilterSection.hideSection = true;
    }

    const sections: Array<FilterSection> = [this.filterSections.salesPersonFilterSection];

    if (hasAccessToTeams) {
      sections.push(this.filterSections.salesTeamFilterSection);
    }

    canAccessPipeline
      ? sections.push(this.filterSections.salesStatusFilterSection)
      : sections.push(this.filterSections.noOpportunityStatusFilterSection);

    if (canAccessPartnerLeads) {
      sections.push(this.filterSections.businessPrioritiesSection);
    }

    canAccessPipeline
      ? sections.push(this.filterSections.salesPersonActionFilterSection)
      : sections.push(this.filterSections.noOpportunityActionFilterSection);

    sections.push(
      ...[
        this.filterSections.createdDateFilterSection,
        this.filterSections.archivedFilterSection,
        this.filterSections.onCampaignFilterSection,
        this.filterSections.snapshotSentFilterSection,
        this.filterSections.moreOptionsFilterSection,
        this.filterSections.lifecycleFilterSection,
      ],
    );
    return new Filters(title, sections);
  }

  ngOnDestroy(): void {
    this.subscriptions.destroy();
  }

  ngAfterViewInit(): void {
    this.filterSections.subscribeToTranslator(this.i18n, this.changeDetector);

    this.subscriptions.add(
      this.manageAccountsFiltersService.filterOptions$.pipe(
        switchMap(() => this.manageAccountsFiltersService.loggedInSalesPerson$),
      ),
      (salesperson) => {
        this.setInitialSalespersonFilter(salesperson);
      },
    );
  }

  setInitialSalespersonFilter(salesPersonId: string): void {
    const salesPersonFilter = this.filterSections.salesPersonFilterSection;
    if (!this.manageAccountsFiltersService.filtersSet) {
      this.filterSections.salesPersonFilterSection.selectById(salesPersonId);
      this.filterChangedEvent(salesPersonFilter.filter);
    }
  }

  filterChangedEvent(event: FilterField<any>): void {
    this.manageAccountsFiltersService.pushEvent(event);
  }

  searchEvent($event: string): void {
    this.manageAccountsFiltersService.setSearchTerm($event);
  }

  async applySavedFilter($event: any): Promise<void> {
    this.filterService.applySavedFilter($event);

    return firstValueFrom(this.filterService.fieldValueChanges).then((changes) =>
      changes.forEach((change) => this.filterChangedEvent(change)),
    );
  }

  saveCurrentFilter($event: any): void {
    this.filterService.saveCurrentFilter($event);
  }

  removeSavedFilter($event: any): void {
    this.filterService.removeSavedFilter($event);
  }

  setLifecycleStage(stage: LifecycleStage): void {
    this.filterSections.lifecycleFilter.changeValue({ id: stage });
  }
}
