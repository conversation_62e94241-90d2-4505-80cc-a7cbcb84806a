<va-filter-container
  data-cy="manage-accounts-filter"
  [filters]="filterStructure$ | async"
  [showToolbar]="true"
  [searchable]="true"
  [filterIsOpen]="true"
  [toolbarText]="toolbarTexts$ | async"
  [enabledSaveSearchTerm]="true"
  [contextKey]="'SSC.ManageAccounts'"
  (fieldValueChanged)="filterChangedEvent($event)"
  [savedFilters]="savedFilters$"
  (savedFilterSelected)="applySavedFilter($event)"
  (saveCurrentFilter)="saveCurrentFilter($event)"
  (removeSavedFilter)="removeSavedFilter($event)"
  (searchTermChanged)="searchEvent($event)"
>
  <ng-content select="[header]" header></ng-content>
  <ng-content select="[content]" content></ng-content>
</va-filter-container>
