import { TestScheduler } from 'rxjs/testing';
import { Observable } from 'rxjs';
import { getLifecycleStageFilterOptions, LifecycleStageFilterOption } from './manage-accounts-filters-lifecycle';
import { TranslateService } from '@ngx-translate/core';

let sched: TestScheduler;

class MockTranslateService {
  private translations = {
    'COMMON.LIFECYCLE_STAGES.LEAD': 'Lead',
    'COMMON.LIFECYCLE_STAGES.PROSPECT': 'Prospect',
    'COMMON.LIFECYCLE_STAGES.CUSTOMER': 'Customer',
  };

  stream(): Observable<any> {
    return sched.createColdObservable('-x', { x: this.translations });
  }
}

describe('Manage Accounts Lifecycle Filters', () => {
  describe('Listing all filter options', () => {
    beforeEach(() => (sched = new TestScheduler((actual, expected) => expect(actual).toEqual(expected))));
    afterEach(() => sched.flush());
    it('Should list Lead, Prospect, and Customer', () => {
      const expected: LifecycleStageFilterOption[] = [
        {
          id: 1,
          name: 'Lead',
        },
        {
          id: 2,
          name: 'Prospect',
        },
        {
          id: 3,
          name: 'Customer',
        },
      ];
      const actual$ = getLifecycleStageFilterOptions(new MockTranslateService() as unknown as TranslateService);

      sched.expectObservable(actual$).toBe('-x', { x: expected });
    });
  });
});
