@use 'design-tokens' as *;

.aieo-nav-tabs {
  display: flex;
  gap: $spacing-2;
  margin-bottom: $spacing-4;
  padding: 0 $spacing-3;
  border-bottom: 1px solid $border-color;

  .nav-tab {
    padding: $spacing-2 $spacing-4;
    border-radius: $default-border-radius $default-border-radius 0 0;
    border: 1px solid transparent;
    border-bottom: none;
    background: transparent;
    color: $secondary-text-color;
    transition: all 0.2s ease;

    mat-icon {
      margin-right: $spacing-2;
    }

    &:hover {
      background: $secondary-background-color;
      color: $primary-text-color;
    }

    &.active {
      background: $card-background-color;
      color: $primary-color;
      border-color: $border-color;
      border-bottom-color: $card-background-color;
      margin-bottom: -1px;
    }
  }
}

.reports-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  padding: $spacing-4;
  background: linear-gradient(135deg, $primary-background-color 0%, $secondary-background-color 100%);
}

.reports-header {
  text-align: center;
  margin-bottom: $spacing-5;
  max-width: 600px;
}

.reports-title {
  @include text-preset-1;
  color: $primary-text-color;
  margin-bottom: $spacing-3;
  line-height: 1.2;

  @media (max-width: 768px) {
    @include text-preset-2;
  }

  @media (max-width: 480px) {
    @include text-preset-3;
  }
}

.reports-subtitle {
  @include text-preset-4;
  color: $secondary-text-color;
  margin: 0;
  line-height: 1.6;

  @media (max-width: 768px) {
    @include text-preset-4;
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  width: 100%;
  max-width: 500px;

  .loading-card {
    width: 100%;
    border-radius: $default-border-radius * 3;
    box-shadow: 0 $spacing-2 $spacing-5 $shadow-color;
    border: none;
    background: $card-background-color;
    overflow: hidden;

    .loading-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: $spacing-3;
      padding: $spacing-4;

      .loading-icon {
        font-size: $spacing-5;
        width: $spacing-5;
        height: $spacing-5;
        color: $secondary-text-color;
        animation: spin 2s linear infinite;
      }

      p {
        margin: 0;
        color: $secondary-text-color;
        @include text-preset-4;
      }
    }
  }
}

.reports-list {
  width: 100%;
  max-width: 1200px;

  .reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: $spacing-4;
    margin-top: $spacing-4;

    .report-card {
      border: 1px solid $border-color;
      border-radius: $default-border-radius * 3;
      box-shadow: 0 $spacing-1 $spacing-2 rgba(0, 0, 0, 0.08);
      transition: all 0.2s ease;
      overflow: hidden;
      background: $card-background-color;

      &:hover {
        box-shadow: 0 $spacing-1 $spacing-4 rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
      }

      mat-card-content {
        padding: $spacing-4;
      }

      .report-header-row {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: $spacing-3;
        gap: $spacing-3;

        .report-domain-section {
          flex: 1;
          min-width: 0;

          .report-domain {
            @include text-preset-3;
            font-weight: 600;
            color: $primary-color;
            margin: 0 0 $spacing-1 0;
            word-break: break-all;
            line-height: 1.4;
          }

          .report-brand {
            @include text-preset-5;
            color: $secondary-text-color;
            font-weight: 500;
            margin: 0;
          }
        }

        .report-status-section {
          flex-shrink: 0;

          .status-badge {
            display: flex;
            align-items: center;
            gap: $spacing-1;

            .status-icon {
              font-size: $spacing-2;
              width: $spacing-2;
              height: $spacing-2;
            }

            .status-text {
              font-weight: 500;
            }
          }
        }
      }

      .report-timestamps {
        margin-bottom: $spacing-3;
        padding: $spacing-2 0;
        border-top: 1px solid $weak-border-color;
        border-bottom: 1px solid $weak-border-color;

        .timestamp-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: $spacing-2;

          &:last-child {
            margin-bottom: 0;
          }

          .timestamp-label {
            @include text-preset-5;
            color: $secondary-text-color;
            font-weight: 500;
          }

          .timestamp-value {
            @include text-preset-5;
            color: $primary-text-color;
            font-weight: 400;
          }
        }
      }

      .progress-section {
        margin-bottom: $spacing-3;
        padding: $spacing-3;
        background: $primary-background-color;
        border-radius: $default-border-radius * 2;

        .progress-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: $spacing-2;

          .progress-label {
            @include text-preset-5;
            color: $secondary-text-color;
            font-weight: 500;
          }

          .progress-percentage {
            @include text-preset-5;
            color: $primary-color;
            font-weight: 600;
          }
        }

        .progress-bar {
          height: $spacing-1;
          border-radius: $spacing-1;
        }
      }

      .report-actions {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding-top: $spacing-3;
        border-top: 1px solid $weak-border-color;

        .action-button {
          min-width: 140px;
          height: 40px;
          border-radius: $default-border-radius * 2;
          font-weight: 500;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: $spacing-2;
          transition: all 0.2s ease;

          mat-icon {
            font-size: $spacing-3;
            width: $spacing-3;
            height: $spacing-3;
          }

          span {
            @include text-preset-5;
          }

          &:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 $spacing-1 $spacing-2 rgba(0, 0, 0, 0.15);
          }
        }
      }
    }
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  width: 100%;
  max-width: 500px;

  .empty-card {
    width: 100%;
    border-radius: $default-border-radius * 3;
    box-shadow: 0 $spacing-2 $spacing-5 $shadow-color;
    border: none;
    background: $card-background-color;
    overflow: hidden;

    .empty-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      padding: $spacing-5 $spacing-4;
      gap: $spacing-3;

      .empty-icon {
        font-size: $spacing-5 * 1.6;
        width: $spacing-5 * 1.6;
        height: $spacing-5 * 1.6;
        color: $tertiary-text-color;
      }

      h2 {
        @include text-preset-2;
        font-weight: 600;
        color: $primary-text-color;
        margin: 0;
      }

      p {
        color: $secondary-text-color;
        margin: 0;
        @include text-preset-4;
      }
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (max-width: 768px) {
  .reports-container {
    padding: $spacing-3;
  }

  .reports-header {
    margin-bottom: $spacing-4;
  }

  .reports-list {
    .reports-grid {
      grid-template-columns: 1fr;
      gap: $spacing-3;

      .report-card {
        mat-card-content {
          padding: $spacing-3;
        }

        .report-header-row {
          flex-direction: column;
          align-items: stretch;
          gap: $spacing-2;

          .report-status-section {
            align-self: flex-start;
          }
        }

        .report-timestamps {
          .timestamp-item {
            flex-direction: column;
            align-items: flex-start;
            gap: $spacing-1;
          }
        }

        .report-actions {
          justify-content: stretch;

          .action-button {
            width: 100%;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .reports-container {
    padding: $spacing-1;
  }

  .reports-header {
    margin-bottom: $spacing-3;
  }

  .reports-list {
    .reports-grid {
      .report-card {
        mat-card-content {
          padding: $spacing-3;
        }

        .report-header-row {
          .report-domain-section {
            .report-domain {
              @include text-preset-4;
            }
          }
        }
      }
    }
  }
}
