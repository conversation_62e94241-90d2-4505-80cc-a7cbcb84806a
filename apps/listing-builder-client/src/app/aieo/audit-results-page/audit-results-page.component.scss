@use 'design-tokens' as *;

.results-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: $spacing-4;
}

.results-header {
  text-align: center;
  margin-bottom: $spacing-5;
}

.results-title {
  @include text-preset-1;
  font-weight: 600;
  color: $primary-text-color;
  margin-bottom: $spacing-2;
}

.results-subtitle {
  @include text-preset-3;
  color: $secondary-text-color;
  margin: 0;
}

.no-data-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.no-data-card {
  max-width: 400px;
  text-align: center;
  padding: $spacing-5;
}

.no-data-card p {
  @include text-preset-3;
  color: $secondary-text-color;
  margin-bottom: $spacing-4;
}

.no-data-card button {
  margin-top: $spacing-2;
}

// Responsive design
@media (max-width: 768px) {
  .results-container {
    padding: $spacing-3;
  }

  .results-title {
    @include text-preset-2;
  }

  .results-subtitle {
    @include text-preset-4;
  }

  .no-data-card {
    padding: $spacing-4;
  }
}
